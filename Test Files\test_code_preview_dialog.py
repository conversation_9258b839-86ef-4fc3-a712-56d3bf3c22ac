#!/usr/bin/env python3
"""
Test script to verify the CodePreviewDialog works correctly.
This test bypasses the unified response panel to test the dialog directly.
"""

import sys
import os

# Add the parent directory to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt6.QtWidgets import QA<PERSON><PERSON>, QMainWindow, QVBoxLayout, QWidget, QPushButton
from ui.code_preview_dialog import CodePreviewDialog

class CodePreviewTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Code Preview Dialog Test")
        self.setGeometry(100, 100, 400, 300)
        
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Test HTML button
        self.test_html_btn = QPushButton("Test HTML Preview (Direct)")
        self.test_html_btn.clicked.connect(self.test_html_preview)
        layout.addWidget(self.test_html_btn)
        
        # Test malformed HTML button
        self.test_malformed_btn = QPushButton("Test 'plaintexthtml' Preview")
        self.test_malformed_btn.clicked.connect(self.test_malformed_preview)
        layout.addWidget(self.test_malformed_btn)
        
    def test_html_preview(self):
        """Test with proper HTML language identifier."""
        html_content = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test HTML Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            color: #333;
            padding: 20px;
        }
        .test-box {
            background-color: #e3f2fd;
            border: 2px solid #2196f3;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🎉 Test HTML Page</h1>
    <p>This is a test HTML page to verify the preview functionality.</p>
    <div class="test-box">
        <h2>Test Box</h2>
        <p>This box should have a blue border and light blue background.</p>
        <button onclick="alert('Hello from test HTML!')">Click me!</button>
    </div>
    <script>
        console.log("HTML preview loaded successfully!");
    </script>
</body>
</html>'''
        
        print("Testing HTML preview with normal 'html' language...")
        dialog = CodePreviewDialog(html_content, 'html', self)
        dialog.exec()
        
    def test_malformed_preview(self):
        """Test with malformed 'plaintexthtml' language identifier."""
        html_content = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Malformed Language Test</title>
    <style>
        body { 
            background: linear-gradient(45deg, #ff9800, #f44336);
            color: white;
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
        }
        .highlight { 
            background-color: rgba(255,255,255,0.2);
            border-radius: 10px;
            padding: 20px;
            margin: 20px;
        }
    </style>
</head>
<body>
    <h1>🚀 Malformed Language Test</h1>
    <div class="highlight">
        <p>This HTML came from a 'plaintexthtml' language identifier.</p>
        <p>The preview should still work correctly!</p>
    </div>
    <script>
        alert("This HTML was originally tagged as 'plaintexthtml' but should still work!");
    </script>
</body>
</html>'''
        
        print("Testing HTML preview with 'plaintexthtml' language (like OpenRouter)...")
        dialog = CodePreviewDialog(html_content, 'plaintexthtml', self)
        dialog.exec()

def main():
    app = QApplication(sys.argv)
    window = CodePreviewTestWindow()
    window.show()
    
    print("Code Preview Dialog Test")
    print("=======================")
    print("This test directly creates CodePreviewDialog instances")
    print("to verify the preview functionality works correctly.")
    print()
    print("Check the console for debug output when opening dialogs.")
    print("Try both the 'Open in Browser' and 'Save File' buttons.")
    print()
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())