# improved_theme.py - Improved readable theme based on user feedback

from PyQt6.QtCore import QPropertyAnimation, QEasingCurve, QRect, QTimer
from PyQt6.QtWidgets import QGraphicsDropShadowEffect
from PyQt6.QtGui import QColor
import os


class ImprovedTheme:
    """Improved theme with better readability and contrast based on user feedback."""
    
    def __init__(self):
        self.current_theme = "light"  # Default to light theme for better readability
        self.setup_themes()
    
    def setup_themes(self):
        """Set up improved color schemes with better contrast."""
        self.themes = {
            "light": {
                # Background colors - cleaner, more spacious
                "bg_primary": "#ffffff",        # Pure white
                "bg_secondary": "#f8f9fa",      # Very light gray
                "bg_tertiary": "#e9ecef",       # Light gray
                "bg_card": "#ffffff",           # Pure white cards
                "bg_input": "#ffffff",          # White inputs
                "bg_hover": "#f1f3f4",          # Very light hover
                
                # Text colors - high contrast
                "text_primary": "#212529",      # Very dark gray
                "text_secondary": "#495057",    # Dark gray  
                "text_muted": "#6c757d",        # Medium gray
                "text_placeholder": "#adb5bd",  # Light gray
                
                # Border colors - subtle but visible
                "border": "#dee2e6",            # Light border
                "border_light": "#e9ecef",      # Very light border
                "border_focus": "#0d6efd",      # Blue focus
                
                # Primary colors - accessible blue
                "primary": "#0d6efd",           # Bootstrap blue
                "primary_hover": "#0b5ed7",     # Darker blue
                "primary_light": "#6ea8fe",     # Light blue
                
                # Status colors
                "success": "#198754",           # Green
                "warning": "#fd7e14",           # Orange
                "error": "#dc3545",             # Red
                "info": "#0dcaf0",              # Cyan
                
                # Agent colors (more subtle, better contrast)
                "agent_1": "#8b5cf6",           # Purple
                "agent_2": "#06b6d4",           # Cyan
                "agent_3": "#10b981",           # Emerald
                "agent_4": "#f59e0b",           # Amber
                "agent_5": "#ef4444",           # Red
            },
            "dark": {
                # Background colors - softer dark theme
                "bg_primary": "#1a1d23",        # Very dark blue-gray
                "bg_secondary": "#212529",      # Dark gray
                "bg_tertiary": "#343a40",       # Medium gray
                "bg_card": "#212529",           # Dark gray cards
                "bg_input": "#343a40",          # Medium gray inputs
                "bg_hover": "#495057",          # Light gray hover
                
                # Text colors - high contrast for dark theme
                "text_primary": "#ffffff",      # Pure white
                "text_secondary": "#e9ecef",    # Very light gray
                "text_muted": "#adb5bd",        # Light gray
                "text_placeholder": "#6c757d",  # Medium gray
                
                # Border colors
                "border": "#495057",            # Medium gray border
                "border_light": "#6c757d",      # Light gray border
                "border_focus": "#6ea8fe",      # Light blue focus
                
                # Primary colors
                "primary": "#6ea8fe",           # Light blue
                "primary_hover": "#9ec5fe",     # Very light blue
                "primary_light": "#cfe2ff",     # Pale blue
                
                # Status colors - adjusted for dark theme
                "success": "#20c997",           # Teal
                "warning": "#ffc107",           # Yellow
                "error": "#f8d7da",             # Light red
                "info": "#9ec5fe",              # Light blue
                
                # Agent colors
                "agent_1": "#a78bfa",           # Light purple
                "agent_2": "#22d3ee",           # Light cyan
                "agent_3": "#34d399",           # Light emerald
                "agent_4": "#fbbf24",           # Light amber
                "agent_5": "#fb7185",           # Light red
            }
        }
    
    def get_color(self, color_name):
        """Get color from current theme."""
        return self.themes[self.current_theme].get(color_name, "#000000")
    
    def toggle_theme(self):
        """Toggle between dark and light themes."""
        self.current_theme = "light" if self.current_theme == "dark" else "dark"
    
    def get_main_window_style(self):
        """Get main window stylesheet with improved spacing."""
        return f"""
        QMainWindow {{
            background-color: {self.get_color('bg_primary')};
            color: {self.get_color('text_primary')};
            font-family: 'Segoe UI', 'San Francisco', 'Helvetica Neue', sans-serif;
            font-size: 14px;
        }}
        
        /* Improved Scrollbars */
        QScrollBar:vertical {{
            background-color: {self.get_color('bg_secondary')};
            width: 14px;
            border: none;
            border-radius: 7px;
            margin: 2px;
        }}
        
        QScrollBar::handle:vertical {{
            background-color: {self.get_color('border')};
            border-radius: 5px;
            min-height: 30px;
            margin: 2px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background-color: {self.get_color('text_muted')};
        }}
        
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            border: none;
            background: none;
        }}
        """
    
    def get_button_style(self, variant="primary"):
        """Get improved button stylesheet."""
        if variant == "primary":
            bg_color = self.get_color('primary')
            hover_color = self.get_color('primary_hover')
            text_color = "#ffffff"
        elif variant == "secondary":
            bg_color = self.get_color('bg_tertiary')
            hover_color = self.get_color('bg_hover')
            text_color = self.get_color('text_primary')
        elif variant == "success":
            bg_color = self.get_color('success')
            hover_color = "#157347"  # Darker green
            text_color = "#ffffff"
        elif variant == "warning":
            bg_color = self.get_color('warning')
            hover_color = "#e25d0e"  # Darker orange
            text_color = "#ffffff"
        elif variant == "danger":
            bg_color = self.get_color('error')
            hover_color = "#bb2d3b"  # Darker red
            text_color = "#ffffff"
        else:
            bg_color = self.get_color('bg_tertiary')
            hover_color = self.get_color('bg_hover')
            text_color = self.get_color('text_primary')
        
        return f"""
        QPushButton {{
            background-color: {bg_color};
            color: {text_color};
            border: 1px solid {self.get_color('border')};
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: 500;
            font-size: 14px;
            min-height: 32px;
            min-width: 80px;
        }}
        
        QPushButton:hover {{
            background-color: {hover_color};
            border-color: {self.get_color('border_focus')};
        }}
        
        QPushButton:pressed {{
            background-color: {hover_color};
        }}
        
        QPushButton:disabled {{
            background-color: {self.get_color('bg_hover')};
            color: {self.get_color('text_muted')};
            border-color: {self.get_color('border_light')};
        }}
        """
    
    def get_input_style(self):
        """Get improved input field stylesheet."""
        return f"""
        QTextEdit, QLineEdit, QSpinBox {{
            background-color: {self.get_color('bg_input')};
            color: {self.get_color('text_primary')};
            border: 1px solid {self.get_color('border')};
            border-radius: 4px;
            padding: 8px 12px;
            font-size: 14px;
            selection-background-color: {self.get_color('primary_light')};
            selection-color: {self.get_color('text_primary')};
        }}
        
        QTextEdit:focus, QLineEdit:focus, QSpinBox:focus {{
            border: 2px solid {self.get_color('border_focus')};
            outline: none;
        }}
        
        QTextEdit::placeholder, QLineEdit::placeholder {{
            color: {self.get_color('text_placeholder')};
        }}
        
        QSpinBox::up-button, QSpinBox::down-button {{
            background-color: {self.get_color('bg_tertiary')};
            border: 1px solid {self.get_color('border')};
            border-radius: 2px;
            width: 18px;
            height: 14px;
        }}
        
        QSpinBox::up-button:hover, QSpinBox::down-button:hover {{
            background-color: {self.get_color('bg_hover')};
        }}
        """
    
    def get_combobox_style(self):
        """Get improved combobox stylesheet."""
        return f"""
        QComboBox {{
            background-color: {self.get_color('bg_input')};
            color: {self.get_color('text_primary')};
            border: 1px solid {self.get_color('border')};
            border-radius: 4px;
            padding: 6px 8px;
            font-size: 14px;
            min-height: 32px;
        }}
        
        QComboBox:focus {{
            border: 2px solid {self.get_color('border_focus')};
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 25px;
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
            background-color: {self.get_color('bg_tertiary')};
        }}
        
        QComboBox::down-arrow {{
            image: none;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 6px solid {self.get_color('text_secondary')};
            margin-right: 8px;
        }}
        
        QComboBox QAbstractItemView {{
            background-color: {self.get_color('bg_card')};
            color: {self.get_color('text_primary')};
            border: 1px solid {self.get_color('border')};
            border-radius: 4px;
            padding: 4px;
            selection-background-color: {self.get_color('primary_light')};
            selection-color: {self.get_color('text_primary')};
        }}
        
        QComboBox QAbstractItemView::item {{
            padding: 6px 8px;
            border-radius: 2px;
            min-height: 24px;
        }}
        
        QComboBox QAbstractItemView::item:hover {{
            background-color: {self.get_color('bg_hover')};
        }}
        """
    
    def get_card_style(self, agent_number=None):
        """Get improved card/panel stylesheet with better spacing."""
        border_color = self.get_color('border')
        if agent_number and agent_number <= 5:
            border_color = self.get_color(f'agent_{agent_number}')
        
        return f"""
        QFrame {{
            background-color: {self.get_color('bg_card')};
            border: 2px solid {border_color};
            border-radius: 8px;
            padding: 16px;
            margin: 8px 4px;
        }}
        
        QFrame:hover {{
            border-color: {self.get_color('border_focus')};
        }}
        """
    
    def get_tab_style(self):
        """Get improved tab widget stylesheet."""
        return f"""
        QTabWidget::pane {{
            border: 1px solid {self.get_color('border')};
            border-radius: 4px;
            background-color: {self.get_color('bg_card')};
            margin-top: 2px;
        }}
        
        QTabWidget::tab-bar {{
            alignment: left;
        }}
        
        QTabBar::tab {{
            background-color: {self.get_color('bg_secondary')};
            color: {self.get_color('text_secondary')};
            border: 1px solid {self.get_color('border')};
            border-bottom: none;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            padding: 8px 16px;
            margin-right: 2px;
            font-weight: 500;
            min-width: 80px;
        }}
        
        QTabBar::tab:selected {{
            background-color: {self.get_color('bg_card')};
            color: {self.get_color('text_primary')};
            border-bottom: 1px solid {self.get_color('bg_card')};
        }}
        
        QTabBar::tab:hover:!selected {{
            background-color: {self.get_color('bg_hover')};
            color: {self.get_color('text_primary')};
        }}
        """
    
    def get_checkbox_style(self):
        """Get improved checkbox stylesheet."""
        return f"""
        QCheckBox {{
            color: {self.get_color('text_primary')};
            font-size: 14px;
            spacing: 8px;
        }}
        
        QCheckBox::indicator {{
            width: 18px;
            height: 18px;
            border: 2px solid {self.get_color('border')};
            border-radius: 3px;
            background-color: {self.get_color('bg_input')};
        }}
        
        QCheckBox::indicator:hover {{
            border-color: {self.get_color('border_focus')};
        }}
        
        QCheckBox::indicator:checked {{
            background-color: {self.get_color('primary')};
            border-color: {self.get_color('primary')};
            image: none;
        }}
        
        QCheckBox::indicator:checked::after {{
            content: "✓";
            color: white;
            font-weight: bold;
            font-size: 12px;
        }}
        """
    
    def get_label_style(self, variant="default"):
        """Get improved label stylesheet."""
        if variant == "heading":
            return f"""
            QLabel {{
                color: {self.get_color('text_primary')};
                font-size: 20px;
                font-weight: 600;
                margin: 8px 0px;
            }}
            """
        elif variant == "subheading":
            return f"""
            QLabel {{
                color: {self.get_color('text_primary')};
                font-size: 16px;
                font-weight: 500;
                margin: 6px 0px;
            }}
            """
        elif variant == "caption":
            return f"""
            QLabel {{
                color: {self.get_color('text_muted')};
                font-size: 12px;
                font-weight: 400;
            }}
            """
        else:
            return f"""
            QLabel {{
                color: {self.get_color('text_primary')};
                font-size: 14px;
                font-weight: 400;
            }}
            """
    
    def get_list_style(self):
        """Get improved list widget stylesheet."""
        return f"""
        QListWidget {{
            background-color: {self.get_color('bg_card')};
            color: {self.get_color('text_primary')};
            border: 1px solid {self.get_color('border')};
            border-radius: 4px;
            padding: 4px;
            font-size: 14px;
        }}
        
        QListWidget::item {{
            padding: 8px 12px;
            border-radius: 3px;
            margin: 1px;
        }}
        
        QListWidget::item:hover {{
            background-color: {self.get_color('bg_hover')};
        }}
        
        QListWidget::item:selected {{
            background-color: {self.get_color('primary_light')};
            color: {self.get_color('text_primary')};
        }}
        """
    
    def add_shadow_effect(self, widget, blur_radius=10, offset=(0, 2)):
        """Add subtle shadow effect (optional - only if it improves readability)."""
        if self.current_theme == "light":  # Only add shadows in light theme
            shadow = QGraphicsDropShadowEffect()
            shadow.setBlurRadius(blur_radius)
            shadow.setColor(QColor(0, 0, 0, 25))  # Very subtle shadow
            shadow.setOffset(offset[0], offset[1])
            widget.setGraphicsEffect(shadow)


# Global improved theme instance
improved_theme = ImprovedTheme()