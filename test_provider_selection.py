#!/usr/bin/env python3
"""
Test script to verify that the correct embedding provider is being selected.
"""

import sys
from pathlib import Path

# Add the current directory to the path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent))

from rag_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>, EmbeddingProvider

def test_provider_selection():
    """Test that the correct embedding provider is selected."""
    
    print("Testing embedding provider selection...")
    
    # Test 1: Explicit Gemini provider
    print("\n1. Testing explicit Gemini provider:")
    try:
        rag_handler = RAGHandler(
            persist_directory="./test_provider_kb",
            embedding_provider="gemini",
            dimension=1536
        )
        print(f"   ✅ Provider: {rag_handler.embedding_provider}")
        print(f"   ✅ Model: {rag_handler.embedding_model_name}")
        print(f"   ✅ Dimension: {rag_handler.dimension}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: Default from config
    print("\n2. Testing default provider from config:")
    try:
        rag_handler = RAGHandler(
            persist_directory="./test_provider_kb2"
        )
        print(f"   ✅ Provider: {rag_handler.embedding_provider}")
        print(f"   ✅ Model: {rag_handler.embedding_model_name}")
        print(f"   ✅ Dimension: {rag_handler.dimension}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Sentence Transformer fallback
    print("\n3. Testing Sentence Transformer fallback:")
    try:
        rag_handler = RAGHandler(
            persist_directory="./test_provider_kb3",
            embedding_provider="sentence_transformer",
            dimension=768
        )
        print(f"   ✅ Provider: {rag_handler.embedding_provider}")
        print(f"   ✅ Model: {rag_handler.embedding_model_name}")
        print(f"   ✅ Dimension: {rag_handler.dimension}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Clean up test directories
    import shutil
    for test_dir in ["./test_provider_kb", "./test_provider_kb2", "./test_provider_kb3"]:
        try:
            if Path(test_dir).exists():
                shutil.rmtree(test_dir)
        except:
            pass
    
    print("\n✅ Provider selection test completed!")

if __name__ == "__main__":
    test_provider_selection()
