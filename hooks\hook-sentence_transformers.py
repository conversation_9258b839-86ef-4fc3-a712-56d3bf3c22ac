# PyInstaller hook for sentence-transformers
from PyInstaller.utils.hooks import collect_all, collect_data_files

# Collect all sentence-transformers data and dependencies
datas, binaries, hiddenimports = collect_all('sentence_transformers')

# Add additional data files that might be missed
datas += collect_data_files('sentence_transformers')

# Add hidden imports that are commonly missed
hiddenimports += [
    'sentence_transformers.models',
    'sentence_transformers.models.Transformer',
    'sentence_transformers.models.Pooling',
    'sentence_transformers.models.Dense',
    'sentence_transformers.models.Normalize',
    'sentence_transformers.evaluation',
    'sentence_transformers.util',
    'sentence_transformers.cross_encoder',
    'transformers.models.auto',
    'transformers.models.bert',
    'transformers.models.distilbert',
    'transformers.models.roberta',
    'torch.nn.functional',
    'torch.nn.modules.sparse',
    'torch.nn.modules.linear',
    'torch.nn.modules.activation',
    'torch.nn.modules.normalization',
    'torch.nn.modules.dropout',
    'torch.nn.modules.container',
    'torch.nn.modules.pooling',
    'torch.nn.modules.conv',
    'torch.nn.modules.batchnorm',
    'torch.nn.modules.instancenorm',
    'torch.nn.modules.groupnorm',
    'torch.nn.modules.layernorm',
    'torch.nn.modules.rnn',
    'torch.nn.modules.transformer',
    'torch.nn.modules.loss',
    'torch.nn.modules.padding',
    'torch.nn.modules.upsampling',
    'torch.nn.modules.fold',
    'torch.nn.modules.flatten',
    'torch.nn.modules.channelshuffle',
    'torch.nn.modules.pixelshuffle',
    'torch.nn.modules.utils',
    'torch.nn.init',
    'torch.nn.parameter',
    'torch.optim',
    'torch.optim.lr_scheduler',
    'torch.utils.data',
    'torch.utils.data.dataloader',
    'torch.utils.data.dataset',
    'torch.utils.data.sampler',
    'torch.utils.data.distributed',
    'torch.utils.checkpoint',
    'torch.utils.model_zoo',
    'torch.hub',
    'torch.jit',
    'torch.onnx',
    'torch.quantization',
    'torch.distributed',
    'torch.multiprocessing',
    'torch.autograd',
    'torch.autograd.function',
    'torch.autograd.gradcheck',
    'torch.autograd.profiler',
    'torch.cuda',
    'torch.backends',
    'torch.backends.cudnn',
    'torch.backends.mkldnn',
    'torch.backends.openmp',
    'torch.backends.quantized',
    'torch.random',
    'torch.sparse',
    'torch.storage',
    'torch.tensor',
    'torch.testing',
    'torch.version'
] 