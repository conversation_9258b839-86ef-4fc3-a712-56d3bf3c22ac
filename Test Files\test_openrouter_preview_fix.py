#!/usr/bin/env python3
"""
Simple test to verify the OpenRouter preview button fix.
This test simulates the exact scenario where OpenRouter models
would stream code but not show preview buttons.
"""

import sys
import os

# Add the parent directory to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QTextEdit, QPushButton
from PyQt6.QtCore import QTimer
from ui.unified_response_panel import UnifiedResponsePanel

class OpenRouterPreviewTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("OpenRouter Preview Button Fix Test")
        self.setGeometry(100, 100, 900, 700)
        
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Create response panel
        self.response_panel = UnifiedResponsePanel()
        layout.addWidget(self.response_panel)
        
        # Create test button
        self.test_button = QPushButton("Run OpenRouter Code Test")
        self.test_button.clicked.connect(self.run_test)
        layout.addWidget(self.test_button)
        
        # Create status text
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(150)
        self.status_text.setReadOnly(True)
        layout.addWidget(self.status_text)
        
    def run_test(self):
        """Run the OpenRouter preview button test."""
        self.status_text.clear()
        self.status_text.append("Starting OpenRouter preview button test...")
        
        # Clear previous tests
        self.response_panel.clear()
        
        # Simulate OpenRouter streaming that previously failed
        self.simulate_openrouter_streaming()
        
        # Use QTimer to allow UI to update before checking results
        QTimer.singleShot(100, self.check_results)
        
    def simulate_openrouter_streaming(self):
        """Simulate OpenRouter streaming code generation."""
        # This simulates the exact pattern that was failing with OpenRouter
        chunks = [
            "I'll create a Python function for you:\n\n```python\n",
            "def calculate_fibonacci(n):\n",
            "    \"\"\"Calculate fibonacci number using recursion.\"\"\"\n",
            "    if n <= 1:\n",
            "        return n\n",
            "    return calculate_fibonacci(n-1) + calculate_fibonacci(n-2)\n",
            "\n",
            "def main():\n",
            "    # Test the function\n",
            "    for i in range(10):\n",
            "        print(f\"Fibonacci({i}) = {calculate_fibonacci(i)}\")\n",
            "\n",
            "if __name__ == '__main__':\n",
            "    main()\n",
            # Note: Missing closing ``` - this is the key issue with OpenRouter
        ]
        
        self.status_text.append("Simulating OpenRouter streaming...")
        
        # Stream the chunks
        for i, chunk in enumerate(chunks):
            is_first_chunk = (i == 0)
            self.response_panel.add_agent_discussion(chunk, 1, "OpenRouter/test-model", is_first_chunk)
            
        self.status_text.append("Streaming complete. Finalizing response...")
        
        # This is the key fix - explicitly finalize the response
        self.response_panel.finalize_agent_response(1)
        
        self.status_text.append("Response finalized.")
        
    def check_results(self):
        """Check if the preview buttons were created successfully."""
        code_blocks = len(self.response_panel._code_blocks)
        
        self.status_text.append(f"\nResults:")
        self.status_text.append(f"- Code blocks detected: {code_blocks}")
        
        if code_blocks > 0:
            self.status_text.append("✅ SUCCESS: Preview buttons should now be visible!")
            self.status_text.append("The OpenRouter preview button issue has been fixed.")
            
            # Show details about the code blocks
            for i, block in enumerate(self.response_panel._code_blocks):
                self.status_text.append(f"  Block {i+1}: {block['language']} ({len(block['content'])} chars)")
        else:
            self.status_text.append("❌ FAILED: No code blocks were detected.")
            self.status_text.append("The fix may not be working properly.")
            
        self.status_text.append(f"\nStreaming contexts: {len(self.response_panel._streaming_contexts)}")

def main():
    app = QApplication(sys.argv)
    window = OpenRouterPreviewTest()
    window.show()
    
    print("OpenRouter Preview Button Fix Test")
    print("===================================")
    print("This test simulates the OpenRouter streaming issue where code")
    print("would be detected during streaming but preview buttons wouldn't")
    print("appear because the code blocks weren't properly finalized.")
    print()
    print("Click 'Run OpenRouter Code Test' to test the fix.")
    print()
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())