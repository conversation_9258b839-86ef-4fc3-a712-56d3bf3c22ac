#!/usr/bin/env python3
"""
Simple test for Gemini embeddings API integration.
Tests the core functionality without complex knowledge base operations.
"""

import os
import sys
import logging
import numpy as np
from pathlib import Path

# Add the current directory to the path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent))

from rag_handler import <PERSON><PERSON><PERSON>and<PERSON>, EmbeddingProvider

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger("GeminiSimpleTest")

def test_basic_embeddings():
    """Test basic Gemini embeddings functionality."""
    
    logger.info("Testing basic Gemini embeddings...")
    
    try:
        # Create a fresh temporary directory for this test
        test_dir = Path("./test_temp_kb")
        if test_dir.exists():
            import shutil
            shutil.rmtree(test_dir)
        
        # Initialize RAG handler with Gemini embeddings
        rag_handler = <PERSON>GHand<PERSON>(
            persist_directory=str(test_dir),
            embedding_provider=EmbeddingProvider.GEMINI,
            dimension=1536
        )
        
        logger.info(f"✅ Initialized RAG handler with provider: {rag_handler.embedding_provider}")
        logger.info(f"✅ Embedding dimension: {rag_handler.dimension}")
        
        # Test simple text embeddings
        test_texts = [
            "Hello world",
            "Machine learning is fascinating",
            "Python programming"
        ]
        
        logger.info("Testing document embeddings...")
        doc_embeddings = rag_handler._get_embeddings(test_texts, context_type="document")
        
        if doc_embeddings is not None and len(doc_embeddings) > 0:
            logger.info(f"✅ Generated {len(doc_embeddings)} embeddings with shape {doc_embeddings.shape}")
            
            # Check if embeddings are not all zeros
            if np.any(doc_embeddings != 0):
                logger.info("✅ Embeddings contain non-zero values")
                
                # Test different task types
                query_embeddings = rag_handler._get_embeddings(["What is machine learning?"], context_type="query")
                if query_embeddings is not None and len(query_embeddings) > 0:
                    logger.info(f"✅ Generated query embedding with shape {query_embeddings.shape}")
                else:
                    logger.warning("⚠️ Query embeddings are empty or None")
                
                # Test task type mapping
                task_type = rag_handler._get_gemini_task_type_for_context("similarity")
                if task_type == "SEMANTIC_SIMILARITY":
                    logger.info("✅ Task type mapping works correctly")
                else:
                    logger.warning(f"⚠️ Unexpected task type: {task_type}")
                
                return True
            else:
                logger.warning("⚠️ All embeddings are zero - this suggests the API is not working properly")
                return False
        else:
            logger.error("❌ Failed to generate embeddings")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    finally:
        # Clean up
        if 'test_dir' in locals() and test_dir.exists():
            import shutil
            shutil.rmtree(test_dir)
            logger.info("🧹 Cleaned up test directory")

def test_api_connection():
    """Test if we can connect to the Gemini API directly."""
    
    logger.info("Testing direct Gemini API connection...")
    
    try:
        import google.generativeai as genai
        from config_manager import ConfigManager
        
        config_manager = ConfigManager()
        api_key = config_manager.get('GEMINI_API_KEY')
        
        if not api_key:
            logger.error("❌ No Gemini API key found")
            return False
        
        # Configure the API
        genai.configure(api_key=api_key)
        
        # Test a simple embedding request
        result = genai.embed_content(
            model="models/gemini-embedding-001",
            content="Hello, this is a test.",
            task_type="SEMANTIC_SIMILARITY"
        )
        
        if hasattr(result, 'embedding') and result.embedding:
            logger.info(f"✅ Direct API call successful, embedding length: {len(result.embedding)}")
            return True
        else:
            logger.error("❌ Direct API call returned empty result")
            return False
            
    except Exception as e:
        logger.error(f"❌ Direct API test failed: {e}")
        return False

def main():
    """Run the simple tests."""
    
    logger.info("=" * 50)
    logger.info("GEMINI EMBEDDINGS SIMPLE TEST")
    logger.info("=" * 50)
    
    tests = [
        ("Direct API Connection", test_api_connection),
        ("Basic Embeddings", test_basic_embeddings)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running test: {test_name}")
        logger.info("-" * 30)
        
        if test_func():
            passed += 1
            logger.info(f"✅ {test_name} PASSED")
        else:
            logger.error(f"❌ {test_name} FAILED")
    
    logger.info("\n" + "=" * 50)
    logger.info(f"RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 ALL TESTS PASSED!")
        return True
    else:
        logger.error("❌ Some tests failed.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
