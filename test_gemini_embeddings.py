#!/usr/bin/env python3
"""
Test script for enhanced Gemini embeddings integration.
Tests the new features including task types and dimensionality control.
"""

import os
import sys
import logging
import numpy as np
from pathlib import Path

# Add the current directory to the path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent))

from rag_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>, EmbeddingProvider

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger("GeminiEmbeddingsTest")

def test_gemini_embeddings():
    """Test the enhanced Gemini embeddings functionality."""
    
    logger.info("Starting Gemini embeddings integration test...")
    
    try:
        # Initialize RAG handler with Gemini embeddings
        rag_handler = <PERSON><PERSON><PERSON>andler(
            persist_directory="./test_knowledge_base",
            embedding_provider=EmbeddingProvider.GEMINI,
            dimension=1536  # Use recommended dimension
        )
        
        logger.info(f"Initialized RAG handler with provider: {rag_handler.embedding_provider}")
        logger.info(f"Embedding dimension: {rag_handler.dimension}")
        
        # Test texts for different scenarios
        test_texts = [
            "What is the meaning of life?",
            "How does machine learning work?",
            "Python is a programming language.",
            "The weather is nice today.",
            "Artificial intelligence is transforming industries."
        ]
        
        # Test document embeddings (default)
        logger.info("Testing document embeddings...")
        doc_embeddings = rag_handler._get_embeddings(test_texts, context_type="document")
        logger.info(f"Generated {len(doc_embeddings)} document embeddings with shape {doc_embeddings.shape}")
        
        # Test query embeddings
        logger.info("Testing query embeddings...")
        query_embeddings = rag_handler._get_embeddings(["What is AI?"], context_type="query")
        logger.info(f"Generated query embedding with shape {query_embeddings.shape}")
        
        # Test semantic similarity embeddings
        logger.info("Testing semantic similarity embeddings...")
        similarity_embeddings = rag_handler._get_embeddings(test_texts[:2], context_type="similarity")
        logger.info(f"Generated similarity embeddings with shape {similarity_embeddings.shape}")
        
        # Calculate cosine similarity between first two texts
        if len(similarity_embeddings) >= 2:
            # Normalize embeddings
            norm1 = np.linalg.norm(similarity_embeddings[0])
            norm2 = np.linalg.norm(similarity_embeddings[1])
            
            if norm1 > 0 and norm2 > 0:
                cosine_sim = np.dot(similarity_embeddings[0], similarity_embeddings[1]) / (norm1 * norm2)
                logger.info(f"Cosine similarity between '{test_texts[0]}' and '{test_texts[1]}': {cosine_sim:.4f}")
        
        # Test adding documents to knowledge base
        logger.info("Testing document addition to knowledge base...")
        test_doc_path = Path("test_document.txt")
        
        # Create a test document
        with open(test_doc_path, 'w', encoding='utf-8') as f:
            f.write("This is a test document for Gemini embeddings integration.\n")
            f.write("It contains information about artificial intelligence and machine learning.\n")
            f.write("The new Gemini embeddings API provides enhanced features for RAG systems.\n")
        
        try:
            # Add document to knowledge base
            rag_handler.add_file(str(test_doc_path))
            logger.info("Successfully added test document to knowledge base")
            
            # Test retrieval
            logger.info("Testing document retrieval...")
            results = rag_handler.get_relevant_chunks(
                query="What are the new features of Gemini embeddings?",
                n_results=3,
                query_type="question"
            )
            
            logger.info(f"Retrieved {len(results)} relevant chunks")
            for i, result in enumerate(results):
                logger.info(f"Result {i+1}: Score={result.get('score', 'N/A'):.4f}, "
                          f"Text='{result.get('text', '')[:100]}...'")
                          
        finally:
            # Clean up test file
            if test_doc_path.exists():
                test_doc_path.unlink()
                logger.info("Cleaned up test document")
        
        logger.info("✅ All Gemini embeddings tests passed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_task_type_mapping():
    """Test the task type mapping functionality."""
    
    logger.info("Testing task type mapping...")
    
    try:
        rag_handler = RAGHandler(embedding_provider=EmbeddingProvider.GEMINI)
        
        # Test different context types
        test_cases = [
            ("document", "RETRIEVAL_DOCUMENT"),
            ("query", "RETRIEVAL_QUERY"),
            ("similarity", "SEMANTIC_SIMILARITY"),
            ("classification", "CLASSIFICATION"),
            ("clustering", "CLUSTERING"),
            ("question", "QUESTION_ANSWERING"),
            ("code_query", "CODE_RETRIEVAL_QUERY"),
            ("fact_check", "FACT_VERIFICATION"),
            ("unknown", "RETRIEVAL_DOCUMENT")  # Should fall back to default
        ]
        
        for context_type, expected_task_type in test_cases:
            actual_task_type = rag_handler._get_gemini_task_type_for_context(context_type)
            if actual_task_type == expected_task_type:
                logger.info(f"✅ {context_type} -> {actual_task_type}")
            else:
                logger.error(f"❌ {context_type} -> {actual_task_type} (expected {expected_task_type})")
                return False
        
        logger.info("✅ Task type mapping test passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Task type mapping test failed: {e}")
        return False

def main():
    """Run all tests."""
    
    logger.info("=" * 60)
    logger.info("GEMINI EMBEDDINGS INTEGRATION TEST SUITE")
    logger.info("=" * 60)
    
    # Check if Gemini API key is available
    from config_manager import ConfigManager
    config_manager = ConfigManager()
    
    if not config_manager.get('GEMINI_API_KEY'):
        logger.error("❌ GEMINI_API_KEY not found in configuration. Please set it to run tests.")
        return False
    
    tests = [
        ("Task Type Mapping", test_task_type_mapping),
        ("Gemini Embeddings Integration", test_gemini_embeddings)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running test: {test_name}")
        logger.info("-" * 40)
        
        if test_func():
            passed += 1
            logger.info(f"✅ {test_name} PASSED")
        else:
            logger.error(f"❌ {test_name} FAILED")
    
    logger.info("\n" + "=" * 60)
    logger.info(f"TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 ALL TESTS PASSED! Gemini embeddings integration is working correctly.")
        return True
    else:
        logger.error("❌ Some tests failed. Please check the logs above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
