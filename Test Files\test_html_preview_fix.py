#!/usr/bin/env python3
"""
Test script to verify the HTML preview fix for plaintexthtml language identifier.
"""

import sys
import os

# Add the parent directory to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QTextEdit, QPushButton
from PyQt6.QtCore import QTimer
from ui.unified_response_panel import UnifiedResponsePanel

class HTMLPreviewTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("HTML Preview Fix Test")
        self.setGeometry(100, 100, 900, 700)
        
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Create response panel
        self.response_panel = UnifiedResponsePanel()
        layout.addWidget(self.response_panel)
        
        # Create test button
        self.test_button = QPushButton("Test plaintexthtml -> HTML Preview")
        self.test_button.clicked.connect(self.run_test)
        layout.addWidget(self.test_button)
        
        # Create status text
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(150)
        self.status_text.setReadOnly(True)
        layout.addWidget(self.status_text)
        
    def run_test(self):
        """Run the HTML preview test."""
        self.status_text.clear()
        self.status_text.append("Testing plaintexthtml -> HTML preview...")
        
        # Clear previous tests
        self.response_panel.clear()
        
        # Test the exact HTML code from the user's example
        html_code = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AuraRing: Intuitive Intelligence at Your Fingertip</title>
    <style>
        body {
            font-family: 'Lato', sans-serif;
            background-color: #121212;
            color: #E0E0E0;
            margin: 0;
            padding: 0;
        }
        .hero {
            text-align: center;
            padding: 2rem;
        }
    </style>
</head>
<body>
    <section class="hero">
        <h1>AuraRing: Intuitive Intelligence at Your Fingertip</h1>
        <p>The future of wellness is seamless.</p>
    </section>
</body>
</html>'''
        
        # Simulate OpenRouter streaming with plaintexthtml
        chunks = [
            "Here's the HTML code:\n```plaintexthtml\n",
            html_code,
            "\n```\n"
        ]
        
        self.status_text.append("Simulating OpenRouter streaming with 'plaintexthtml'...")
        
        # Stream the chunks
        for i, chunk in enumerate(chunks):
            is_first_chunk = (i == 0)
            self.response_panel.add_agent_discussion(chunk, 1, "mistralai/devstral-medium", is_first_chunk)
        
        # Finalize the response
        self.response_panel.finalize_agent_response(1)
        
        # Use QTimer to allow UI to update before checking results
        QTimer.singleShot(100, self.check_results)
        
    def check_results(self):
        """Check if the HTML preview was created correctly."""
        code_blocks = len(self.response_panel._code_blocks)
        
        self.status_text.append(f"\nResults:")
        self.status_text.append(f"- Code blocks detected: {code_blocks}")
        
        if code_blocks > 0:
            block = self.response_panel._code_blocks[0]
            self.status_text.append(f"- Language detected: '{block['language']}'")
            self.status_text.append(f"- Content length: {len(block['content'])} characters")
            
            if block['language'].lower() == 'html':
                self.status_text.append("✅ SUCCESS: plaintexthtml was normalized to HTML!")
                self.status_text.append("The preview button should now work correctly for HTML.")
            else:
                self.status_text.append(f"❌ FAILED: Language is '{block['language']}' instead of 'html'")
        else:
            self.status_text.append("❌ FAILED: No code blocks were detected.")
        
        # Test normalization directly
        normalized = self.response_panel._normalize_language('plaintexthtml')
        self.status_text.append(f"\nDirect normalization test:")
        self.status_text.append(f"'plaintexthtml' -> '{normalized}'")

def main():
    app = QApplication(sys.argv)
    window = HTMLPreviewTest()
    window.show()
    
    print("HTML Preview Fix Test")
    print("====================")
    print("This test verifies that 'plaintexthtml' is properly")
    print("normalized to 'html' and creates a working HTML preview.")
    print()
    print("Click the test button to run the verification.")
    print()
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())