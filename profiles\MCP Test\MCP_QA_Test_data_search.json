{"agent_count": 2, "general_instructions": "This is a two-agent system for evaluating the retrieval quality of different Large Language Models using RAG. Agent 1 is the Retriever, tasked with finding relevant information from the knowledge base. Agent 2 is the Assessor, responsible for scoring the quality of the retrieved data against a ground truth.", "knowledge_base_path": "knowledge_base", "agents": [{"provider": "Anthropic", "model": "claude-3-7-sonnet-20250219", "instructions": "You are Agent 1, the Retriever. Your sole purpose is to analyze the user's prompt and retrieve the most relevant and accurate chunks of text from the provided knowledge base using RAG. You should not synthesize, summarize, or alter the retrieved information. Your output should be the raw, retrieved data that you believe best answers the user's query.\n\nsearch for details of Smart systems, shared goals: The complementarity of artificial intelligence and digital public infrastructure  section on page 184 in the C:\\Users\\<USER>\\Desktop\\Large PDF Files", "thinking_enabled": false}, {"provider": "Google GenAI", "model": "gemini-2.5-pro-preview-06-05", "instructions": "\nYou are Agent 2, the Assessor. Your role is to provide a strict, quantitative, and qualitative evaluation of the information retrieved by Agent 1. You have access to the ground truth context and must use it to score the retriever's performance.\n\nYour primary goal is to make the evaluation easy to read and the scores immediately visible.\n\n**Evaluation Metrics (1-5 scale):**\n\n* **Retrieval Precision:** How relevant was the retrieved information to the user's prompt? (1=Irrelevant, 5=Highly Relevant)\n* **Retrieval Recall:** Was all the necessary information to answer the prompt retrieved? (1=Very little retrieved, 5=All relevant information retrieved)\n\n**Output Format:**\n\nYour entire response MUST be in the following Markdown format. Do not add any extra commentary before or after the formatted output.\n\n```markdown\n## RAG Retrieval Evaluation\n\n**Overall Score**: [Average of the two scores]/5\n\n| Metric              | Score (1-5) |\n|---------------------|-------------|\n| Retrieval Precision | [Score]     |\n| Retrieval Recall    | [Score]     |\n\n---\n\n### Key Observations & Recommendations\n\n**Strengths**:\n* [Briefly describe a key strength of the retrieval, with a specific example if possible. e.g., 'Successfully retrieved the exact paragraph defining the core concept.']\n\n**Areas for Improvement**:\n* [Briefly describe a key weakness, with a specific example if possible. e.g., 'Missed the section containing critical exceptions to the main rule.']\n\n**Recommendation**:\n* [Provide a single, concise recommendation for improving the retrieval model's performance.]\n\n**Ground Truth Context:** - this section is purely for your information do not include it in your answer\nBox 6.2 Smart systems, shared goals: The complementarity of artificial intelligence and digital public infrastructure Traditionally, infrastructure has been associated with physical assets such as roads, electricity grids and water systems that provide essential services for public use. Digital public infrastructure is a multidimensional approach to national digital transformation that relies on both physical and virtual systems. At its core, digital public infrastructure is about building and managing digital systems that support essential services in today’s society. These systems include proving one’s identity online, sending money quickly and securely and sharing information safely—with the right privacy protections and consent.1 Services aim to be inclusive so no one is left out, foundational so others can build on them, interoperable through open standards that can support diverse uses and publicly accountable to ensure they serve the public interest rather than private or siloed goals.2 Digital public infrastructure can speed up the use of AI. Many AI applications need both unstructured and structured data. Structured data often come from different government registries and databases, which are usually spread across ministries, departments and agencies. For example, in India AI is helping farmers get real-time support, including access to insurance and subsidies in their local languages—something that depends on combining many different data sources.3 AI can enhance digital public infrastructure. Unlike traditional infrastructure, digital public infrastructure is highly scalable, adaptable and reusable, offering unprecedented innovation potential. For instance, Stripe—a global payments platform—uses machine learning to spot signs of fraud by analysing unusual transaction patterns, shifts in purchasing behaviour and changes in device details.4 Similarly, AI powers biometric authentication in digital ID systems, which is especially useful where fingerprint recognition does not work well. This approach has been promoting inclusion, as, for example, many agricultural and manual workers face fingerprint erosion, making alternative biometric methods more reliable.5 Despite the growing potential, research on the causal links between digital public infrastructure and AI remains limited. More work is needed to understand how these two concepts can reinforce each other, what risks their interaction may pose and how policymakers should approach their integration, ensuring that benefits are widely distributed and reinforcing human agency, trust and fairness in the digital age.6 Notes 1. Eaves and Sandman 2021. 2. Eaves, Mazzucato and Vasconcellos 2024. 3. D’Silva and others 2019. 4. Adams 2025. 5. Digital public infrastructure can be vulnerable to serious threats, such as disinformation campaigns that undermine public confidence. A notable example comes from Brazil, where false information about a new regulation related to Pix—an instant digital payment platform—circulated widely, impacting more than 9.4 million people in 2025 (Luciano and Fleck 2025). 6. Rikap 2024.\nadvancements have tended to be accompanied by larger capital investment and higher capital shares of income.58 The relevant lens for tax policy may thus involve rebalancing capital and labour taxation to equitably distribute productivity gains and encourage investment in labour-complementing technology.59 The design of such taxation matters and should be carefully considered. For example, while taxing specific technologies—for example, a “robot tax”—may hamper innovation in a particular field,60 broader instruments such as capital income tax may achieve both efficiency and equity.61 AI itself can be leveraged as a tool for improving tax revenue by enhancing compliance and increasing administrative efficiency. AI-driven tools can help governments monitor complex financial transactions, detect fraud and reduce evasion.62 Strengthening tax systems is important for developing economies, which struggle with closing tax revenue gaps and increasing the tax-to-GDP ratio beyond 15 percent—a threshold associated with positive effects of taxation on economic growth and development.63 Expanding fiscal space through improved revenue collection can in turn fund critical complementarity investment—in education, skills development and digital infrastructure. Beyond taxation public investment in research and development of labour-enhancing AI, along with strategic subsidies for firms to adopt these types of technologies, can tip the balance towards AI as an enabler for augmentation and innovation.64 Public–private partnerships can drive labour-enhancing AI innovation and bridge gaps between research and development, business cases and societal needs (see box 6.4 later in the chapter). For example, in Mexico a newly established private sector–academia collaboration, \n", "thinking_enabled": false}]}