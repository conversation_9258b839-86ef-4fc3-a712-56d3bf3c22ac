# modern_widgets.py - Modern styled widgets and components

from PyQt6.QtWidgets import (
    QPushButton, QLabel, QFrame, QHBoxLayout, QVBoxLayout, 
    QTextEdit, QLineEdit, QComboBox, QCheckBox, QSpinBox,
    QWidget, QGraphicsDropShadowEffect, QScrollArea
)
from PyQt6.QtCore import Qt, QPropertyAnimation, QEasingCurve, QTimer, pyqtSignal
from PyQt6.QtGui import QColor, QFont, QPainter, QPaintEvent, QBrush, QPen
from ui.modern_theme import modern_theme


class ModernButton(QPushButton):
    """Modern styled button with hover effects and variants."""
    
    def __init__(self, text="", variant="primary", icon=None, parent=None):
        super().__init__(text, parent)
        self.variant = variant
        self.setup_style()
        
        if icon:
            self.setIcon(icon)
    
    def setup_style(self):
        """Apply modern styling to button."""
        self.setStyleSheet(modern_theme.get_button_style(self.variant))
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        # Add shadow effect
        modern_theme.add_shadow_effect(self, blur_radius=10, offset=(0, 2))


class ModernCard(QFrame):
    """Modern card widget with rounded corners and shadow."""
    
    def __init__(self, title="", agent_number=None, parent=None):
        super().__init__(parent)
        self.agent_number = agent_number
        self.setup_style()
        self.setup_layout(title)
    
    def setup_style(self):
        """Apply modern card styling."""
        self.setStyleSheet(modern_theme.get_card_style(self.agent_number))
        self.setFrameStyle(QFrame.Shape.StyledPanel)
        
        # Add shadow effect
        modern_theme.add_shadow_effect(self, blur_radius=15, offset=(0, 4))
    
    def setup_layout(self, title):
        """Set up card layout with optional title."""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(20, 20, 20, 20)
        self.layout.setSpacing(16)
        
        if title:
            title_label = ModernLabel(title, variant="subheading")
            self.layout.addWidget(title_label)
    
    def add_widget(self, widget):
        """Add widget to card layout."""
        self.layout.addWidget(widget)
    
    def add_layout(self, layout):
        """Add layout to card layout."""
        self.layout.addLayout(layout)


class ModernLabel(QLabel):
    """Modern styled label with typography variants."""
    
    def __init__(self, text="", variant="default", parent=None):
        super().__init__(text, parent)
        self.variant = variant
        self.setup_style()
    
    def setup_style(self):
        """Apply modern label styling."""
        self.setStyleSheet(modern_theme.get_label_style(self.variant))
        self.setWordWrap(True)


class ModernInput(QLineEdit):
    """Modern styled input field."""
    
    def __init__(self, placeholder="", parent=None):
        super().__init__(parent)
        if placeholder:
            self.setPlaceholderText(placeholder)
        self.setup_style()
    
    def setup_style(self):
        """Apply modern input styling."""
        self.setStyleSheet(modern_theme.get_input_style())


class ModernTextEdit(QTextEdit):
    """Modern styled text edit area."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
    
    def setup_style(self):
        """Apply modern text edit styling."""
        self.setStyleSheet(modern_theme.get_input_style())


class ModernComboBox(QComboBox):
    """Modern styled combo box."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
    
    def setup_style(self):
        """Apply modern combo box styling."""
        self.setStyleSheet(modern_theme.get_combobox_style())


class ModernCheckBox(QCheckBox):
    """Modern styled checkbox."""
    
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setup_style()
    
    def setup_style(self):
        """Apply modern checkbox styling."""
        self.setStyleSheet(modern_theme.get_checkbox_style())


class ModernSpinBox(QSpinBox):
    """Modern styled spin box."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
    
    def setup_style(self):
        """Apply modern spin box styling."""
        self.setStyleSheet(modern_theme.get_input_style())


class AnimatedWidget(QWidget):
    """Widget with built-in animations."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.fade_animation = None
        self.slide_animation = None
    
    def fade_in(self, duration=300):
        """Fade in animation."""
        self.setWindowOpacity(0.0)
        self.show()
        
        self.fade_animation = modern_theme.create_fade_animation(self, duration)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.start()
    
    def fade_out(self, duration=300, hide_after=True):
        """Fade out animation."""
        self.fade_animation = modern_theme.create_fade_animation(self, duration)
        self.fade_animation.setStartValue(1.0)
        self.fade_animation.setEndValue(0.0)
        
        if hide_after:
            self.fade_animation.finished.connect(self.hide)
        
        self.fade_animation.start()


class ModernProgressIndicator(QWidget):
    """Modern loading/progress indicator."""
    
    def __init__(self, size=40, parent=None):
        super().__init__(parent)
        self.size = size
        self.angle = 0
        self.timer = QTimer()
        self.timer.timeout.connect(self.rotate)
        self.setFixedSize(size, size)
    
    def start(self):
        """Start the animation."""
        self.timer.start(50)  # Update every 50ms
        self.show()
    
    def stop(self):
        """Stop the animation."""
        self.timer.stop()
        self.hide()
    
    def rotate(self):
        """Rotate the indicator."""
        self.angle = (self.angle + 10) % 360
        self.update()
    
    def paintEvent(self, event: QPaintEvent):
        """Paint the progress indicator."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Clear background
        painter.fillRect(self.rect(), QColor(0, 0, 0, 0))
        
        # Draw spinning circle
        center = self.rect().center()
        radius = self.size // 2 - 4
        
        painter.setPen(QPen(QColor(modern_theme.get_color('primary')), 3, Qt.PenStyle.SolidLine))
        painter.translate(center)
        painter.rotate(self.angle)
        
        # Draw arc
        painter.drawArc(-radius, -radius, radius * 2, radius * 2, 0, 240 * 16)


class ModernToggleSwitch(QWidget):
    """Modern toggle switch widget."""
    
    toggled = pyqtSignal(bool)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.checked = False
        self.animation = None
        self.circle_position = 3
        self.setFixedSize(60, 30)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
    
    def mousePressEvent(self, event):
        """Handle mouse press to toggle."""
        if event.button() == Qt.MouseButton.LeftButton:
            self.toggle()
    
    def toggle(self):
        """Toggle the switch state."""
        self.checked = not self.checked
        self.animate_toggle()
        self.toggled.emit(self.checked)
    
    def animate_toggle(self):
        """Animate the toggle transition."""
        self.animation = QPropertyAnimation(self, b"circle_position")
        self.animation.setDuration(200)
        self.animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        if self.checked:
            self.animation.setEndValue(33)  # Move to right
        else:
            self.animation.setEndValue(3)   # Move to left
        
        self.animation.valueChanged.connect(self.update)
        self.animation.start()
    
    def paintEvent(self, event: QPaintEvent):
        """Paint the toggle switch."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Draw background track
        track_color = QColor(modern_theme.get_color('primary') if self.checked 
                           else modern_theme.get_color('bg_hover'))
        painter.setBrush(QBrush(track_color))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRoundedRect(0, 0, 60, 30, 15, 15)
        
        # Draw circle
        circle_color = QColor("#ffffff")
        painter.setBrush(QBrush(circle_color))
        painter.drawEllipse(self.circle_position, 3, 24, 24)


class ResponsiveLayout(QVBoxLayout):
    """Responsive layout that adapts to screen size."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setContentsMargins(20, 20, 20, 20)
        self.setSpacing(16)
    
    def add_responsive_widget(self, widget, stretch=0):
        """Add widget with responsive behavior."""
        self.addWidget(widget, stretch)
        
        # Add responsive behavior based on widget type
        if isinstance(widget, ModernCard):
            widget.setMinimumWidth(300)


class ModernScrollArea(QScrollArea):
    """Modern styled scroll area."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
    
    def setup_style(self):
        """Apply modern scroll area styling."""
        self.setStyleSheet(f"""
        QScrollArea {{
            border: none;
            background-color: transparent;
        }}
        
        QScrollArea > QWidget > QWidget {{
            background-color: transparent;
        }}
        """)
        self.setWidgetResizable(True)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)


class GlassEffect(QFrame):
    """Glass morphism effect widget."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
    
    def setup_style(self):
        """Apply glass effect styling."""
        self.setStyleSheet(f"""
        QFrame {{
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }}
        """)


class ModernSeparator(QFrame):
    """Modern styled separator line."""
    
    def __init__(self, orientation=Qt.Orientation.Horizontal, parent=None):
        super().__init__(parent)
        self.setup_style(orientation)
    
    def setup_style(self, orientation):
        """Apply separator styling."""
        if orientation == Qt.Orientation.Horizontal:
            self.setFrameShape(QFrame.Shape.HLine)
            self.setFixedHeight(1)
        else:
            self.setFrameShape(QFrame.Shape.VLine)
            self.setFixedWidth(1)
        
        self.setStyleSheet(f"""
        QFrame {{
            background-color: {modern_theme.get_color('border')};
            border: none;
        }}
        """)