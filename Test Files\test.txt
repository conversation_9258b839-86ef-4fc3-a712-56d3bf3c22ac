import turtle
import time
import random

# Game Configuration
SCREEN_WIDTH = 600
SCREEN_HEIGHT = 600
SEGMENT_SIZE = 20
INITIAL_DELAY = 0.1
MIN_DELAY = 0.05
DELAY_DECREMENT = 0.001

# Game State Variables
score = 0
high_score = 0
game_paused = False
game_over = False
difficulty_level = 1
delay = INITIAL_DELAY

# Load high score from file
try:
    with open("high_score.txt", "r") as f:
        high_score = int(f.read().strip())
except:
    high_score = 0

# Set up the screen
wn = turtle.Screen()
wn.title("Enhanced Snake Game - Level 1")
wn.bgcolor("green")
wn.setup(width=SCREEN_WIDTH, height=SCREEN_HEIGHT)
wn.tracer(0)  # Turns off screen updates for smoother animation

# Snake head
head = turtle.Turtle()
head.speed(0)
head.shape("square")
head.color("black")
head.penup()
head.goto(0, 0)
head.direction = "stop"

# Snake food
food = turtle.Turtle()
food.speed(0)
food.shape("circle")
food.color("red")
food.penup()
food.goto(0, 100)

# Special food (appears every 50 points)
special_food = turtle.Turtle()
special_food.speed(0)
special_food.shape("triangle")
special_food.color("gold")
special_food.penup()
special_food.hideturtle()
special_food_active = False

# Snake segments
segments = []

# Scoreboard
pen = turtle.Turtle()
pen.speed(0)
pen.shape("square")
pen.color("white")
pen.penup()
pen.hideturtle()
pen.goto(0, 260)

# Game info display
info_pen = turtle.Turtle()
info_pen.speed(0)
info_pen.color("white")
info_pen.penup()
info_pen.hideturtle()
info_pen.goto(-290, 260)

def update_display():
    """Update score and game info display"""
    pen.clear()
    pen.write(f"Score: {score} High Score: {high_score}", align="center", font=("Courier", 24, "normal"))
    
    info_pen.clear()
    info_pen.write(f"Level: {difficulty_level} | Speed: {int((INITIAL_DELAY - delay) * 1000)}", 
                   align="left", font=("Courier", 12, "normal"))

def save_high_score():
    """Save high score to file"""
    try:
        with open("high_score.txt", "w") as f:
            f.write(str(high_score))
    except:
        pass

# Movement functions
def go_up():
    if head.direction != "down" and not game_paused and not game_over:
        head.direction = "up"

def go_down():
    if head.direction != "up" and not game_paused and not game_over:
        head.direction = "down"

def go_left():
    if head.direction != "right" and not game_paused and not game_over:
        head.direction = "left"

def go_right():
    if head.direction != "left" and not game_paused and not game_over:
        head.direction = "right"

def move():
    """Move the snake head based on current direction"""
    if head.direction == "up":
        y = head.ycor()
        head.sety(y + SEGMENT_SIZE)
    elif head.direction == "down":
        y = head.ycor()
        head.sety(y - SEGMENT_SIZE)
    elif head.direction == "left":
        x = head.xcor()
        head.setx(x - SEGMENT_SIZE)
    elif head.direction == "right":
        x = head.xcor()
        head.setx(x + SEGMENT_SIZE)

def toggle_pause():
    """Toggle game pause state"""
    global game_paused
    if not game_over:
        game_paused = not game_paused
        if game_paused:
            pen.goto(0, 0)
            pen.write("PAUSED - Press SPACE to continue", align="center", font=("Courier", 20, "bold"))
        else:
            update_display()

def reset_game():
    """Reset game to initial state"""
    global score, game_over, game_paused, delay, difficulty_level, special_food_active
    
    if game_over:
        # Save high score if needed
        if score > high_score:
            global high_score
            high_score = score
            save_high_score()
        
        # Reset game state
        score = 0
        game_over = False
        game_paused = False
        delay = INITIAL_DELAY
        difficulty_level = 1
        special_food_active = False
        
        # Clear segments
        for segment in segments:
            segment.goto(1000, 1000)
        segments.clear()
        
        # Reset snake position
        head.goto(0, 0)
        head.direction = "stop"
        
        # Reset food positions
        food.goto(0, 100)
        special_food.hideturtle()
        
        # Update display
        wn.title("Enhanced Snake Game - Level 1")
        update_display()

def game_over_screen():
    """Display game over screen"""
    global game_over
    game_over = True
    
    pen.clear()
    pen.goto(0, 50)
    pen.write("GAME OVER!", align="center", font=("Courier", 40, "bold"))
    pen.goto(0, 0)
    pen.write(f"Final Score: {score}", align="center", font=("Courier", 24, "normal"))
    pen.goto(0, -30)
    if score > high_score:
        pen.write("NEW HIGH SCORE!", align="center", font=("Courier", 20, "bold"))
        pen.goto(0, -60)
    pen.write("Press 'R' to Restart", align="center", font=("Courier", 16, "normal"))

def spawn_special_food():
    """Spawn special food at random location"""
    global special_food_active
    if not special_food_active and score > 0 and score % 50 == 0:
        x = random.randint(-280, 280)
        y = random.randint(-280, 280)
        special_food.goto(x, y)
        special_food.showturtle()
        special_food_active = True

def increase_difficulty():
    """Increase game difficulty based on score"""
    global difficulty_level, delay
    new_level = (score // 100) + 1
    if new_level > difficulty_level:
        difficulty_level = new_level
        wn.title(f"Enhanced Snake Game - Level {difficulty_level}")
        # Increase speed
        if delay > MIN_DELAY:
            delay = max(MIN_DELAY, INITIAL_DELAY - (difficulty_level - 1) * 0.02)

# Keyboard bindings
wn.listen()
wn.onkeypress(go_up, "w")
wn.onkeypress(go_up, "Up")
wn.onkeypress(go_down, "s")
wn.onkeypress(go_down, "Down")
wn.onkeypress(go_left, "a")
wn.onkeypress(go_left, "Left")
wn.onkeypress(go_right, "d")
wn.onkeypress(go_right, "Right")
wn.onkeypress(toggle_pause, "space")
wn.onkeypress(reset_game, "r")

# Initial display update
update_display()

# Main game loop
while True:
    wn.update()
    
    # Skip game logic if paused or game over
    if game_paused or game_over:
        time.sleep(0.1)
        continue
    
    # Check for wall collision
    if (head.xcor() > 290 or head.xcor() < -290 or 
        head.ycor() > 290 or head.ycor() < -290):
        game_over_screen()
        continue
    
    # Check for collision with regular food
    if head.distance(food) < 20:
        # Move food to random spot
        x = random.randint(-280, 280)
        y = random.randint(-280, 280)
        food.goto(x, y)
        
        # Add segment
        new_segment = turtle.Turtle()
        new_segment.speed(0)
        new_segment.shape("square")
        new_segment.color("grey")
        new_segment.penup()
        segments.append(new_segment)
        
        # Increase score
        score += 10
        
        # Spawn special food if conditions met
        spawn_special_food()
        
        # Increase difficulty
        increase_difficulty()
        
        # Update display
        update_display()
    
    # Check for collision with special food
    if special_food_active and head.distance(special_food) < 20:
        special_food.hideturtle()
        special_food_active = False
        
        # Special food gives bonus points
        score += 50
        
        # Add multiple segments
        for _ in range(3):
            new_segment = turtle.Turtle()
            new_segment.speed(0)
            new_segment.shape("square")
            new_segment.color("orange")
            new_segment.penup()
            segments.append(new_segment)
        
        update_display()
    
    # Move segments
    for index in range(len(segments) - 1, 0, -1):
        x = segments[index - 1].xcor()
        y = segments[index - 1].ycor()
        segments[index].goto(x, y)
    
    # Move first segment to head position
    if len(segments) > 0:
        x = head.xcor()
        y = head.ycor()
        segments[0].goto(x, y)
    
    # Move head
    move()
    
    # Check for head collision with body
    for segment in segments:
        if segment.distance(head) < 20:
            game_over_screen()
            break
    
    # Control game speed
    time.sleep(delay)

wn.mainloop()