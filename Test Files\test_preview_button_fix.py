#!/usr/bin/env python3
"""
Test script to verify preview button functionality improvements.
This script tests various scenarios where preview buttons might not appear.
"""

import sys
import os

# Add the parent directory to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from ui.unified_response_panel import UnifiedResponsePanel
import re

class TestPreviewButton:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.window = QMainWindow()
        self.setup_ui()
        
    def setup_ui(self):
        """Setup test UI."""
        central_widget = QWidget()
        self.window.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        self.response_panel = UnifiedResponsePanel()
        layout.addWidget(self.response_panel)
        
        self.window.resize(800, 600)
        self.window.setWindowTitle("Preview Button Test")
        
    def test_regular_markdown_code(self):
        """Test regular markdown code blocks."""
        print("Testing regular markdown code blocks...")
        
        # Test Python code
        self.response_panel.add_agent_discussion("Here's some Python code:\n```python\n", 1, "test-model", True)
        self.response_panel.add_agent_discussion("def hello_world():\n    print('Hello, World!')\n", 1, "test-model", False)
        self.response_panel.add_agent_discussion("```\n", 1, "test-model", False)
        
        # Test JavaScript code
        self.response_panel.add_agent_discussion("And some JavaScript:\n```js\n", 1, "test-model", False)
        self.response_panel.add_agent_discussion("function greet(name) {\n    console.log(`Hello, ${name}!`);\n}\n", 1, "test-model", False)
        self.response_panel.add_agent_discussion("```\n", 1, "test-model", False)
        
        print(f"Code blocks stored: {len(self.response_panel._code_blocks)}")
        
    def test_alternative_language_names(self):
        """Test alternative language names."""
        print("Testing alternative language names...")
        
        # Test with 'py' instead of 'python'
        self.response_panel.add_agent_discussion("Python with 'py' syntax:\n```py\n", 1, "test-model", False)
        self.response_panel.add_agent_discussion("import os\nprint(os.getcwd())\n", 1, "test-model", False)
        self.response_panel.add_agent_discussion("```\n", 1, "test-model", False)
        
        # Test TypeScript
        self.response_panel.add_agent_discussion("TypeScript code:\n```ts\n", 1, "test-model", False)
        self.response_panel.add_agent_discussion("interface User {\n    name: string;\n    age: number;\n}\n", 1, "test-model", False)
        self.response_panel.add_agent_discussion("```\n", 1, "test-model", False)
        
        print(f"Code blocks stored: {len(self.response_panel._code_blocks)}")
        
    def test_unformatted_code_detection(self):
        """Test detection of unformatted code."""
        print("Testing unformatted code detection...")
        
        # Test Python code without markdown
        python_code = """def calculate_fibonacci(n):
    if n <= 1:
        return n
    return calculate_fibonacci(n-1) + calculate_fibonacci(n-2)

result = calculate_fibonacci(10)
print(f"Fibonacci(10) = {result}")"""
        
        # Test if it's detected as unformatted code
        is_detected = self.response_panel._detect_unformatted_code(python_code)
        print(f"Python code detected as unformatted: {is_detected}")
        
        # Test language detection
        detected_language = self.response_panel._detect_language_from_content(python_code)
        print(f"Detected language: {detected_language}")
        
        # Add it to the panel
        self.response_panel.add_agent_discussion(python_code, 1, "test-model", False)
        
    def test_streaming_code_chunks(self):
        """Test code that arrives in streaming chunks."""
        print("Testing streaming code chunks...")
        
        # Simulate how code might arrive in chunks
        chunks = [
            "Let me create a simple calculator:\n```python\n",
            "class Calculator:\n    def __init__(self):\n        self.result = 0\n\n",
            "    def add(self, x):\n        self.result += x\n        return self\n\n",
            "    def multiply(self, x):\n        self.result *= x\n        return self\n\n",
            "    def get_result(self):\n        return self.result\n\n",
            "# Usage example\ncalc = Calculator()\n",
            "result = calc.add(5).multiply(3).get_result()\n",
            "print(f'Result: {result}')\n```\n"
        ]
        
        for i, chunk in enumerate(chunks):
            is_first = i == 0
            self.response_panel.add_agent_discussion(chunk, 2, "streaming-model", is_first)
        
        # Simulate completion signal
        self.response_panel.finalize_agent_response(2)
            
        print(f"Total code blocks: {len(self.response_panel._code_blocks)}")
    
    def test_incomplete_code_blocks(self):
        """Test handling of incomplete code blocks that don't end with ```."""
        print("Testing incomplete code blocks...")
        
        # Simulate code block that doesn't end properly (like OpenRouter issue)
        chunks = [
            "Here's some Python code:\n```python\n",
            "def hello():\n    print('Hello World')\n",
            "    return 'success'\n",
            # Missing closing ``` - this simulates the OpenRouter issue
        ]
        
        for i, chunk in enumerate(chunks):
            is_first = i == 0
            self.response_panel.add_agent_discussion(chunk, 3, "incomplete-model", is_first)
        
        print(f"Code blocks before finalization: {len(self.response_panel._code_blocks)}")
        
        # This should finalize the incomplete code block
        self.response_panel.finalize_agent_response(3)
        
        print(f"Code blocks after finalization: {len(self.response_panel._code_blocks)}")
        
        # Check if the incomplete code block was added
        if self.response_panel._code_blocks:
            last_block = self.response_panel._code_blocks[-1]
            print(f"Last code block language: {last_block['language']}")
            print(f"Last code block content preview: {last_block['content'][:50]}...")
        
        print(f"Total code blocks: {len(self.response_panel._code_blocks)}")
        
    def test_regex_patterns(self):
        """Test the improved regex patterns."""
        print("Testing regex patterns...")
        
        # Test the enhanced code fence regex
        test_strings = [
            "```python\n",
            "```py\n", 
            "```javascript\n",
            "```js\n",
            "```typescript\n",
            "```ts\n",
            "```html\n",
            "```css\n",
            "```json\n",
            "```yaml\n",
            "```bash\n",
            "```shell\n",
            "```sh\n",
            "```\n",  # No language specified
            "```python-3.9\n",  # Version numbers
            "```c++\n",  # Special characters
        ]
        
        pattern = r'(```[a-zA-Z0-9+\-_]*\n?)'
        
        for test_str in test_strings:
            matches = re.split(pattern, test_str)
            print(f"'{test_str.strip()}' -> {matches}")
            
    def run_tests(self):
        """Run all tests."""
        print("Starting preview button tests...")
        
        self.test_regex_patterns()
        self.test_regular_markdown_code()
        self.test_alternative_language_names()
        self.test_unformatted_code_detection()
        self.test_streaming_code_chunks()
        self.test_incomplete_code_blocks()
        
        print(f"\nFinal summary:")
        print(f"Total code blocks with preview: {len(self.response_panel._code_blocks)}")
        
        # Show the window
        self.window.show()
        
        # Run the application
        return self.app.exec()

if __name__ == "__main__":
    test = TestPreviewButton()
    sys.exit(test.run_tests())