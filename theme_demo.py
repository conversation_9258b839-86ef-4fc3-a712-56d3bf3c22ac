#!/usr/bin/env python3
"""
Theme Demo - Quick demonstration of the modern theme system
"""

import sys
from PyQt6.QtWidgets import <PERSON>Application, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel
from PyQt6.QtCore import Qt

# Import our improved theme system
from ui.improved_theme import improved_theme


class ThemeDemo(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Python Agents - Modern Theme Demo")
        self.setGeometry(100, 100, 800, 600)
        
        # Set up the UI
        self.setup_ui()
        
        # Apply initial theme
        self.apply_theme()
    
    def setup_ui(self):
        """Set up the demo UI."""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Title
        title = QLabel("Improved Theme Demo")
        title.setStyleSheet(improved_theme.get_label_style("heading"))
        main_layout.addWidget(title)
        
        # Theme toggle button
        self.toggle_btn = QPushButton("🌙 Switch to Dark Theme")
        self.toggle_btn.setStyleSheet(improved_theme.get_button_style("primary"))
        self.toggle_btn.clicked.connect(self.toggle_theme)
        main_layout.addWidget(self.toggle_btn)
        
        # Demo text
        demo_text = QLabel("The improved theme fixes the readability issues:\n\n"
                          "✅ High contrast text that's easy to read\n"
                          "✅ Proper spacing that isn't cramped\n"
                          "✅ Light theme by default for better visibility\n"
                          "✅ Cleaner borders and subtle shadows\n"
                          "✅ No CSS transform errors\n\n"
                          "Your app should now look much better!")
        demo_text.setStyleSheet(improved_theme.get_label_style())
        demo_text.setWordWrap(True)
        main_layout.addWidget(demo_text)
        
        # Status
        self.status_label = QLabel("Current theme: Light")
        self.status_label.setStyleSheet(improved_theme.get_label_style("caption"))
        main_layout.addWidget(self.status_label)
    
    def apply_theme(self):
        """Apply the current theme to all components."""
        # Apply main window styling
        self.setStyleSheet(improved_theme.get_main_window_style())
        
        # Update toggle button text
        theme_name = "Dark" if improved_theme.current_theme == "dark" else "Light"
        opposite_theme = "Light" if improved_theme.current_theme == "dark" else "Dark"
        icon = "🌙" if improved_theme.current_theme == "light" else "☀️"
        
        self.toggle_btn.setText(f"{icon} Switch to {opposite_theme} Theme")
        self.toggle_btn.setStyleSheet(improved_theme.get_button_style("primary"))
        self.status_label.setText(f"Current theme: {theme_name}")
    
    def toggle_theme(self):
        """Toggle between dark and light themes."""
        improved_theme.toggle_theme()
        self.apply_theme()


def main():
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Python Agents Theme Demo")
    app.setApplicationVersion("1.0")
    
    # Create and show the demo window
    demo = ThemeDemo()
    demo.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()