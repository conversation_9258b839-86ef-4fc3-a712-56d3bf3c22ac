#!/usr/bin/env python3
"""
Test script to verify the malformed language identifier fix.
This test specifically handles the 'plaintexthtml' case from OpenRouter.
"""

import sys
import os

# Add the parent directory to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QTextEdit, QPushButton
from PyQt6.QtCore import QTimer
from ui.unified_response_panel import UnifiedResponsePanel

class MalformedLanguageTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Malformed Language Identifier Test")
        self.setGeometry(100, 100, 900, 700)
        
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Create response panel
        self.response_panel = UnifiedResponsePanel()
        layout.addWidget(self.response_panel)
        
        # Create test button
        self.test_button = QPushButton("Test Malformed Language Identifiers")
        self.test_button.clicked.connect(self.run_test)
        layout.addWidget(self.test_button)
        
        # Create status text
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(200)
        self.status_text.setReadOnly(True)
        layout.addWidget(self.status_text)
        
    def run_test(self):
        """Run the malformed language identifier test."""
        self.status_text.clear()
        self.status_text.append("Testing malformed language identifiers...")
        
        # Clear previous tests
        self.response_panel.clear()
        
        # Test various malformed language identifiers
        test_cases = [
            {
                'language': 'plaintexthtml',
                'content': '<!DOCTYPE html>\n<html>\n<head>\n<title>Test</title>\n</head>\n<body>\n<h1>Hello World</h1>\n</body>\n</html>',
                'expected': 'html'
            },
            {
                'language': 'plaintextcss',
                'content': 'body {\n    background-color: #f0f0f0;\n    font-family: Arial, sans-serif;\n}',
                'expected': 'css'
            },
            {
                'language': 'plaintextjs',
                'content': 'function greet(name) {\n    console.log(`Hello, ${name}!`);\n}',
                'expected': 'javascript'
            },
            {
                'language': 'plaintextpython',
                'content': 'def hello_world():\n    print("Hello, World!")\n    return "success"',
                'expected': 'python'
            }
        ]
        
        agent_number = 1
        for i, test_case in enumerate(test_cases):
            self.status_text.append(f"Testing {test_case['language']} -> {test_case['expected']}")
            
            # Simulate the malformed language identifier
            chunks = [
                f"Here's some {test_case['expected']} code:\n```{test_case['language']}\n",
                test_case['content'],
                "\n```\n"
            ]
            
            for j, chunk in enumerate(chunks):
                is_first_chunk = (j == 0)
                self.response_panel.add_agent_discussion(chunk, agent_number, "test-model", is_first_chunk)
            
            # Finalize the response
            self.response_panel.finalize_agent_response(agent_number)
            agent_number += 1
        
        # Use QTimer to allow UI to update before checking results
        QTimer.singleShot(100, self.check_results)
        
    def check_results(self):
        """Check if the preview buttons were created successfully."""
        code_blocks = len(self.response_panel._code_blocks)
        
        self.status_text.append(f"\nResults:")
        self.status_text.append(f"- Code blocks detected: {code_blocks}")
        
        if code_blocks == 4:  # We tested 4 different malformed languages
            self.status_text.append("✅ SUCCESS: All malformed language identifiers were handled!")
            
            # Show details about the code blocks
            for i, block in enumerate(self.response_panel._code_blocks):
                self.status_text.append(f"  Block {i+1}: {block['language']} ({len(block['content'])} chars)")
                
            self.status_text.append("\nThe OpenRouter 'plaintexthtml' issue has been fixed!")
            
        else:
            self.status_text.append("❌ FAILED: Some malformed language identifiers were not detected.")
            
        # Test the normalize function directly
        self.status_text.append(f"\nDirect normalization tests:")
        test_langs = ['plaintexthtml', 'plaintextcss', 'plaintextjs', 'plaintextpython']
        for lang in test_langs:
            normalized = self.response_panel._normalize_language(lang)
            self.status_text.append(f"  {lang} -> {normalized}")

def main():
    app = QApplication(sys.argv)
    window = MalformedLanguageTest()
    window.show()
    
    print("Malformed Language Identifier Test")
    print("==================================")
    print("This test specifically handles the OpenRouter issue where")
    print("language identifiers like 'plaintexthtml' were not being")
    print("recognized as valid HTML code.")
    print()
    print("Click 'Test Malformed Language Identifiers' to run the test.")
    print()
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())