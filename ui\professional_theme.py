# professional_theme.py - Professional modern theme that actually looks good

from PyQt6.QtCore import Q<PERSON>ropertyAnimation, QEasingCurve, QRect, QTimer
from PyQt6.QtWidgets import QGraphicsDropShadowEffect
from PyQt6.QtGui import QColor
import os


class ProfessionalTheme:
    """Professional modern theme with excellent visual design and readability."""
    
    def __init__(self):
        self.current_theme = "light"  # Default to light theme
        self.setup_themes()
    
    def setup_themes(self):
        """Set up professional color schemes."""
        self.themes = {
            "light": {
                # Background colors - clean and professional
                "bg_primary": "#f8f9fa",        # Very light gray background
                "bg_secondary": "#ffffff",      # Pure white
                "bg_tertiary": "#e9ecef",       # Light gray
                "bg_card": "#ffffff",           # Pure white cards
                "bg_input": "#ffffff",          # White inputs
                "bg_hover": "#f1f3f4",          # Very light hover
                "bg_selected": "#e3f2fd",       # Light blue selection
                
                # Text colors - excellent contrast
                "text_primary": "#1a1a1a",      # Almost black
                "text_secondary": "#404040",    # Dark gray  
                "text_muted": "#6c757d",        # Medium gray
                "text_placeholder": "#adb5bd",  # Light gray
                "text_white": "#ffffff",        # Pure white
                
                # Border colors - subtle and clean
                "border": "#e0e0e0",            # Light border
                "border_light": "#f0f0f0",      # Very light border
                "border_focus": "#2196f3",      # Material blue focus
                "border_hover": "#1976d2",      # Darker blue hover
                
                # Primary colors - Material Design inspired
                "primary": "#2196f3",           # Material blue
                "primary_hover": "#1976d2",     # Darker blue
                "primary_light": "#bbdefb",     # Light blue
                "primary_dark": "#0d47a1",      # Very dark blue
                
                # Status colors - clear and accessible
                "success": "#4caf50",           # Material green
                "success_hover": "#388e3c",     # Darker green
                "warning": "#ff9800",           # Material orange
                "warning_hover": "#f57c00",     # Darker orange
                "error": "#f44336",             # Material red
                "error_hover": "#d32f2f",       # Darker red
                "info": "#2196f3",              # Material blue
                
                # Agent colors - vibrant but professional
                "agent_1": "#9c27b0",           # Purple
                "agent_2": "#00bcd4",           # Cyan
                "agent_3": "#4caf50",           # Green
                "agent_4": "#ff9800",           # Orange
                "agent_5": "#f44336",           # Red
                
                # Special effects
                "shadow": "rgba(0, 0, 0, 0.1)",
                "shadow_hover": "rgba(0, 0, 0, 0.15)",
            },
            "dark": {
                # Background colors - elegant dark theme
                "bg_primary": "#121212",        # Material dark
                "bg_secondary": "#1e1e1e",      # Slightly lighter
                "bg_tertiary": "#2d2d2d",       # Medium dark
                "bg_card": "#1e1e1e",           # Card background
                "bg_input": "#2d2d2d",          # Input background
                "bg_hover": "#3d3d3d",          # Hover state
                "bg_selected": "#1565c0",       # Blue selection
                
                # Text colors - high contrast
                "text_primary": "#ffffff",      # Pure white
                "text_secondary": "#e0e0e0",    # Light gray
                "text_muted": "#9e9e9e",        # Medium gray
                "text_placeholder": "#757575",  # Dark gray
                "text_white": "#ffffff",        # Pure white
                
                # Border colors
                "border": "#404040",            # Medium gray border
                "border_light": "#505050",      # Light gray border
                "border_focus": "#64b5f6",      # Light blue focus
                "border_hover": "#42a5f5",      # Lighter blue hover
                
                # Primary colors - adjusted for dark theme
                "primary": "#64b5f6",           # Light blue
                "primary_hover": "#42a5f5",     # Lighter blue
                "primary_light": "#e3f2fd",     # Very light blue
                "primary_dark": "#1976d2",      # Dark blue
                
                # Status colors
                "success": "#81c784",           # Light green
                "success_hover": "#66bb6a",     # Medium green
                "warning": "#ffb74d",           # Light orange
                "warning_hover": "#ffa726",     # Medium orange
                "error": "#e57373",             # Light red
                "error_hover": "#ef5350",       # Medium red
                "info": "#64b5f6",              # Light blue
                
                # Agent colors - lighter for dark theme
                "agent_1": "#ce93d8",           # Light purple
                "agent_2": "#4dd0e1",           # Light cyan
                "agent_3": "#81c784",           # Light green
                "agent_4": "#ffb74d",           # Light orange
                "agent_5": "#e57373",           # Light red
                
                # Special effects
                "shadow": "rgba(0, 0, 0, 0.3)",
                "shadow_hover": "rgba(0, 0, 0, 0.4)",
            }
        }
    
    def get_color(self, color_name):
        """Get color from current theme."""
        return self.themes[self.current_theme].get(color_name, "#000000")
    
    def toggle_theme(self):
        """Toggle between dark and light themes."""
        self.current_theme = "light" if self.current_theme == "dark" else "dark"
    
    def get_main_window_style(self):
        """Get professional main window stylesheet."""
        return f"""
        QMainWindow {{
            background-color: {self.get_color('bg_primary')};
            color: {self.get_color('text_primary')};
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            font-size: 14px;
            font-weight: 400;
        }}
        
        QMainWindow::separator {{
            background-color: {self.get_color('border')};
            width: 1px;
            height: 1px;
        }}
        
        /* Professional Scrollbars */
        QScrollBar:vertical {{
            background-color: {self.get_color('bg_secondary')};
            width: 16px;
            border: none;
            border-radius: 8px;
            margin: 0px;
        }}
        
        QScrollBar::handle:vertical {{
            background-color: {self.get_color('border')};
            border-radius: 8px;
            min-height: 30px;
            margin: 2px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background-color: {self.get_color('text_muted')};
        }}
        
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            border: none;
            background: none;
            height: 0px;
        }}
        
        QScrollBar:horizontal {{
            background-color: {self.get_color('bg_secondary')};
            height: 16px;
            border: none;
            border-radius: 8px;
            margin: 0px;
        }}
        
        QScrollBar::handle:horizontal {{
            background-color: {self.get_color('border')};
            border-radius: 8px;
            min-width: 30px;
            margin: 2px;
        }}
        
        QScrollBar::handle:horizontal:hover {{
            background-color: {self.get_color('text_muted')};
        }}
        
        QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
            border: none;
            background: none;
            width: 0px;
        }}
        """
    
    def get_button_style(self, variant="primary"):
        """Get professional button stylesheet."""
        if variant == "primary":
            bg_color = self.get_color('primary')
            hover_color = self.get_color('primary_hover')
            text_color = self.get_color('text_white')
            border_color = self.get_color('primary')
        elif variant == "secondary":
            bg_color = self.get_color('bg_secondary')
            hover_color = self.get_color('bg_hover')
            text_color = self.get_color('text_primary')
            border_color = self.get_color('border')
        elif variant == "success":
            bg_color = self.get_color('success')
            hover_color = self.get_color('success_hover')
            text_color = self.get_color('text_white')
            border_color = self.get_color('success')
        elif variant == "warning":
            bg_color = self.get_color('warning')
            hover_color = self.get_color('warning_hover')
            text_color = self.get_color('text_white')
            border_color = self.get_color('warning')
        elif variant == "danger":
            bg_color = self.get_color('error')
            hover_color = self.get_color('error_hover')
            text_color = self.get_color('text_white')
            border_color = self.get_color('error')
        else:
            bg_color = self.get_color('bg_secondary')
            hover_color = self.get_color('bg_hover')
            text_color = self.get_color('text_primary')
            border_color = self.get_color('border')
        
        return f"""
        QPushButton {{
            background-color: {bg_color};
            color: {text_color};
            border: 1px solid {border_color};
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 500;
            font-size: 14px;
            min-height: 36px;
            min-width: 100px;
        }}
        
        QPushButton:hover {{
            background-color: {hover_color};
            border-color: {hover_color};
        }}
        
        QPushButton:pressed {{
            background-color: {hover_color};
            border-color: {hover_color};
        }}
        
        QPushButton:disabled {{
            background-color: {self.get_color('bg_hover')};
            color: {self.get_color('text_muted')};
            border-color: {self.get_color('border_light')};
        }}
        """
    
    def get_input_style(self):
        """Get professional input field stylesheet."""
        return f"""
        QTextEdit, QLineEdit, QSpinBox {{
            background-color: {self.get_color('bg_input')};
            color: {self.get_color('text_primary')};
            border: 2px solid {self.get_color('border')};
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 14px;
            font-family: inherit;
            selection-background-color: {self.get_color('primary_light')};
            selection-color: {self.get_color('text_primary')};
        }}
        
        QTextEdit:focus, QLineEdit:focus, QSpinBox:focus {{
            border: 2px solid {self.get_color('border_focus')};
            outline: none;
        }}
        
        QTextEdit::placeholder, QLineEdit::placeholder {{
            color: {self.get_color('text_placeholder')};
        }}
        
        QSpinBox::up-button, QSpinBox::down-button {{
            background-color: {self.get_color('bg_tertiary')};
            border: 1px solid {self.get_color('border')};
            border-radius: 4px;
            width: 20px;
            height: 16px;
        }}
        
        QSpinBox::up-button:hover, QSpinBox::down-button:hover {{
            background-color: {self.get_color('bg_hover')};
        }}
        """
    
    def get_combobox_style(self):
        """Get professional combobox stylesheet."""
        return f"""
        QComboBox {{
            background-color: {self.get_color('bg_input')};
            color: {self.get_color('text_primary')};
            border: 2px solid {self.get_color('border')};
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 14px;
            min-height: 36px;
            font-family: inherit;
        }}
        
        QComboBox:focus {{
            border: 2px solid {self.get_color('border_focus')};
        }}
        
        QComboBox:hover {{
            border: 2px solid {self.get_color('border_hover')};
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 30px;
            border-top-right-radius: 8px;
            border-bottom-right-radius: 8px;
            background-color: transparent;
        }}
        
        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 6px solid {self.get_color('text_secondary')};
            margin-right: 10px;
        }}
        
        QComboBox QAbstractItemView {{
            background-color: {self.get_color('bg_card')};
            color: {self.get_color('text_primary')};
            border: 2px solid {self.get_color('border')};
            border-radius: 8px;
            padding: 8px;
            selection-background-color: {self.get_color('bg_selected')};
            selection-color: {self.get_color('text_primary')};
            outline: none;
        }}
        
        QComboBox QAbstractItemView::item {{
            padding: 10px 12px;
            border-radius: 4px;
            margin: 2px;
            min-height: 20px;
        }}
        
        QComboBox QAbstractItemView::item:hover {{
            background-color: {self.get_color('bg_hover')};
        }}
        
        QComboBox QAbstractItemView::item:selected {{
            background-color: {self.get_color('bg_selected')};
        }}
        """
    
    def get_card_style(self, agent_number=None):
        """Get professional card/panel stylesheet."""
        border_color = self.get_color('border')
        if agent_number and agent_number <= 5:
            border_color = self.get_color(f'agent_{agent_number}')
        
        return f"""
        QFrame {{
            background-color: {self.get_color('bg_card')};
            border: 2px solid {border_color};
            border-radius: 12px;
            padding: 20px;
            margin: 8px 4px;
        }}
        
        QFrame:hover {{
            border-color: {self.get_color('border_focus')};
        }}
        """
    
    def get_tab_style(self):
        """Get professional tab widget stylesheet."""
        return f"""
        QTabWidget::pane {{
            border: 2px solid {self.get_color('border')};
            border-radius: 8px;
            background-color: {self.get_color('bg_card')};
            margin-top: 2px;
            padding: 8px;
        }}
        
        QTabWidget::tab-bar {{
            alignment: left;
        }}
        
        QTabBar::tab {{
            background-color: {self.get_color('bg_secondary')};
            color: {self.get_color('text_secondary')};
            border: 2px solid {self.get_color('border')};
            border-bottom: none;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            padding: 12px 20px;
            margin-right: 4px;
            font-weight: 500;
            min-width: 100px;
        }}
        
        QTabBar::tab:selected {{
            background-color: {self.get_color('bg_card')};
            color: {self.get_color('text_primary')};
            border-bottom: 2px solid {self.get_color('bg_card')};
            font-weight: 600;
        }}
        
        QTabBar::tab:hover:!selected {{
            background-color: {self.get_color('bg_hover')};
            color: {self.get_color('text_primary')};
        }}
        """
    
    def get_checkbox_style(self):
        """Get professional checkbox stylesheet."""
        return f"""
        QCheckBox {{
            color: {self.get_color('text_primary')};
            font-size: 14px;
            spacing: 10px;
            font-weight: 400;
        }}
        
        QCheckBox::indicator {{
            width: 20px;
            height: 20px;
            border: 2px solid {self.get_color('border')};
            border-radius: 4px;
            background-color: {self.get_color('bg_input')};
        }}
        
        QCheckBox::indicator:hover {{
            border-color: {self.get_color('border_focus')};
        }}
        
        QCheckBox::indicator:checked {{
            background-color: {self.get_color('primary')};
            border-color: {self.get_color('primary')};
        }}
        
        QCheckBox::indicator:checked:hover {{
            background-color: {self.get_color('primary_hover')};
            border-color: {self.get_color('primary_hover')};
        }}
        """
    
    def get_label_style(self, variant="default"):
        """Get professional label stylesheet."""
        if variant == "heading":
            return f"""
            QLabel {{
                color: {self.get_color('text_primary')};
                font-size: 24px;
                font-weight: 600;
                margin: 16px 0px 8px 0px;
            }}
            """
        elif variant == "subheading":
            return f"""
            QLabel {{
                color: {self.get_color('text_primary')};
                font-size: 18px;
                font-weight: 500;
                margin: 12px 0px 6px 0px;
            }}
            """
        elif variant == "caption":
            return f"""
            QLabel {{
                color: {self.get_color('text_muted')};
                font-size: 12px;
                font-weight: 400;
                margin: 4px 0px;
            }}
            """
        else:
            return f"""
            QLabel {{
                color: {self.get_color('text_primary')};
                font-size: 14px;
                font-weight: 400;
                margin: 2px 0px;
            }}
            """
    
    def get_list_style(self):
        """Get professional list widget stylesheet."""
        return f"""
        QListWidget {{
            background-color: {self.get_color('bg_card')};
            color: {self.get_color('text_primary')};
            border: 2px solid {self.get_color('border')};
            border-radius: 8px;
            padding: 8px;
            font-size: 14px;
            outline: none;
        }}
        
        QListWidget::item {{
            padding: 12px 16px;
            border-radius: 6px;
            margin: 2px;
            border: none;
        }}
        
        QListWidget::item:hover {{
            background-color: {self.get_color('bg_hover')};
        }}
        
        QListWidget::item:selected {{
            background-color: {self.get_color('bg_selected')};
            color: {self.get_color('text_primary')};
        }}
        """
    
    def add_shadow_effect(self, widget, blur_radius=12, offset=(0, 4)):
        """Add professional shadow effect."""
        if self.current_theme == "light":  # Only add shadows in light theme
            shadow = QGraphicsDropShadowEffect()
            shadow.setBlurRadius(blur_radius)
            shadow.setColor(QColor(0, 0, 0, 40))  # Subtle shadow
            shadow.setOffset(offset[0], offset[1])
            widget.setGraphicsEffect(shadow)


# Global professional theme instance
professional_theme = ProfessionalTheme()