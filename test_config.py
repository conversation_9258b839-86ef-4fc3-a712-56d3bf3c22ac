#!/usr/bin/env python3
"""
Test configuration loading to debug API key issues.
"""

import sys
from pathlib import Path

# Add the current directory to the path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent))

from config_manager import ConfigManager

def main():
    print("Testing configuration loading...")
    
    config_manager = ConfigManager()
    
    # Check if Gemini API key is loaded
    gemini_key = config_manager.get('GEMINI_API_KEY')
    google_key = config_manager.get('GOOGLE_API_KEY')
    
    print(f"GEMINI_API_KEY found: {bool(gemini_key)}")
    print(f"GOOGLE_API_KEY found: {bool(google_key)}")
    
    if gemini_key:
        print(f"GEMINI_API_KEY starts with: {gemini_key[:10]}...")
    if google_key:
        print(f"GOOGLE_API_KEY starts with: {google_key[:10]}...")
    
    # Check if they're the same
    if gemini_key and google_key:
        print(f"Keys are the same: {gemini_key == google_key}")
    
    # List all config keys
    print("\nAll configuration keys:")
    all_config = config_manager.get_all()
    for key in sorted(all_config.keys()):
        if 'KEY' in key:
            value = all_config[key]
            if value:
                print(f"  {key}: {str(value)[:10]}...")
            else:
                print(f"  {key}: None/Empty")

if __name__ == "__main__":
    main()
