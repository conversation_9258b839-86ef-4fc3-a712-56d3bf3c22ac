#!/usr/bin/env python3
"""
Test script for the updated Knowledge Base Dialog with embedding provider selection.
"""

import sys
from pathlib import Path
from PyQt6.QtWidgets import QApplication

# Add the current directory to the path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent))

from knowledge_base import KnowledgeBaseDialog
from rag_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>

def test_kb_dialog():
    """Test the Knowledge Base Dialog with embedding provider dropdown."""
    
    app = QApplication(sys.argv)
    
    # Create a test RAG handler
    rag_handler = <PERSON>GHandler(
        persist_directory="./test_kb_dialog",
        embedding_provider="sentence_transformer",  # Use local for testing
        dimension=768
    )
    
    # Create and show the dialog
    dialog = KnowledgeBaseDialog(rag_handler)
    dialog.show()
    
    print("Knowledge Base Dialog opened with embedding provider dropdown.")
    print("You should see:")
    print("1. A dropdown menu with embedding provider options")
    print("2. Status text showing dimension and requirements")
    print("3. The dropdown should default to 'Gemini (Latest - Recommended)'")
    
    # Run the application
    sys.exit(app.exec())

if __name__ == "__main__":
    test_kb_dialog()
