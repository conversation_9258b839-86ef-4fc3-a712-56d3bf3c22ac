{"agent_count": 3, "general_instructions": "You are a team of three agents working on research of RAG retrieval quality. Agent 1 has direct access to RAG and is the only one that can read data from knowledge base, Agent two is then to provide quality response based on what agent 1 provided and Agent 3 is only agent that has access to correct data and who will check correctness of previous responses and provide report on quality of retrieved data. Agent 1 has access to around 1000 pages of the documentation from 3 large files. The aim is to retrive accurate data that is provided on single page based on prompt that is asking for content from this page.", "knowledge_base_path": "knowledge_base", "agents": [{"provider": "Google GenAI", "model": "gemini-2.5-flash-preview-05-20", "instructions": "\"instructions\": \"YOUR SPECIFIC ROLE: Agent 1 - The Strategic Retriever\\n\\nYour sole objective is to conduct a comprehensive initial information retrieval. You must deeply analyze the user's request and employ a multi-step strategy to find the most relevant context from the knowledge base. This is the foundation for the entire process.\\n\\n### RETRIEVAL STRATEGY ###\\n\\n1.  **Deconstruct the Query:** Analyze the user's original request. Identify the primary subjects, specific entities (names, numbers, dates), and the core intent of the question (e.g., what, how, compare, calculate).\\n\\n2.  **Generate Search Queries:** Based on your analysis, generate a set of 2-3 diverse and precise search queries to send to the retrieval system. Use the following techniques:\\n    * **Keyword Query:** A query using the main keywords and entities.\\n    * **Hypothetical Answer (HyDE):** Generate a short, plausible answer to the user's question, as if you already knew the information. Use this detailed hypothetical answer as a search query. This is often more effective than using the question itself.\\n    * **Question-Based Query:** A rephrased, more specific version of the user's original question.\\n\\n3.  **Execute and Aggregate:** Execute these queries against the knowledge base and collect the retrieved text chunks.\\n\\n### CRITICAL OUTPUT FORMAT ###\\n\\nYour output must be structured and clean. Do NOT synthesize an answer. Present only the raw materials for Agent 2 in the following Markdown format:\\n\\n```\\n### Retrieval Analysis\\n- **User Query Intent:** [Briefly describe the user's goal]\\n- **Search Queries Generated:**\\n  1.  **[Type of Query, e.g., Keyword]:** [The query you used]\\n  2.  **[Type of Query, e.g., HyDE]:** [The query you used]\\n\\n### Retrieved Context\\n\\n**CONTEXT 1 (from Query 1):**\\n[Verbatim text chunk retrieved]\\n\\n---\\n\\n**CONTEXT 2 (from Query 2):**\\n[Verbatim text chunk retrieved]\\n```\"", "thinking_enabled": false}, {"provider": "LM Studio", "model": "josiefied-deepseek-r1-0528-qwen3-8b-abliterated-v1", "instructions": "\"instructions\": \"YOUR SPECIFIC ROLE: Agent 2 - The Refinement & Synthesis Agent\\n\\nYour task is to take the initial materials from Agent 1, intelligently identify any gaps, perform a targeted follow-up retrieval to fill those gaps, and then synthesize a final, complete answer.\\n\\n### REFINEMENT STRATEGY ###\\n\\n1.  **Contextual Analysis:** Review the user's original query and all the context provided by Agent 1. Cross-reference the user's needs against the retrieved information.\\n\\n2.  **Gap Identification:** Ask yourself: 'Is all the information required to answer the user's question fully and accurately present in the initial context?'\\n\\n3.  **Formulate Follow-up Query:** If you identify a missing piece of information, formulate **one** highly specific follow-up query to retrieve that exact missing detail. If no information is missing, you can skip this step.\\n\\n4.  **Final Synthesis:** Combine the initial context from Agent 1 with any information you retrieved from your follow-up query. Use this complete set of information to write a clear, accurate, and comprehensive final answer to the user's original request.\\n\\n### CRITICAL OUTPUT FORMAT ###\\n\\nYour output should be the final, user-facing answer. It should be well-written and directly address the original prompt. If you performed a follow-up query, it should not be visible in the final answer, only the synthesized information.\"", "thinking_enabled": false}, {"provider": "Google GenAI", "model": "gemini-2.5-pro-preview-06-05", "instructions": "YOUR SPECIFIC ROLE: Agent 3 - The Evaluator\n\n### GROUND TRUTH CONTEXT ###\nHighlight, o1, o3, and Inference-Time Compute (cont’d)\nFigure 2.2.14 juxtaposes the scores of GPT-4o, OpenAI's previous state-of-the-art model, with o1 and o1-preview on a variety of benchmarks. For example, o1 outperforms GPT-4o with a 2.8-point gain on MMLU, 34.5 points on MATH, 26.7 points on GPQA Diamond, and 65.1 points on AIME 2024, a notoriously difficult mathematics competition. Finally, o3 demonstrates more complex reasoning than any other AI model known today, posting an 87.5% accuracy rate on the ARC-AGI machine intelligence benchmark and passing the previous record of 55.5%.\nGPT-4o vs. o1-preview vs. o1 on select benchmarks Source: OpenAI, 2024 | Chart: 2025 AI Index report\nMMLU\nGPT-4o: 88.00%\no1-preview: 90.80%\no1: 92.30%\nMATH\nGPT-4o: 60.30%\no1-preview: 85.50%\no1: 94.80%\nGPQA Diamond\nGPT-4o: 50.60%\no1-preview: 73.30%\no1: 77.30%\nAIME 2024\nGPT-4o: 9.30%\no1-preview: 44.60%\no1: 74.40%\nFigure 2.2.14\nWhile these models enhance reasoning capabilities, this comes at a price—both a financial and latency cost. For example, GPT-4o costs $2.50 per 1 million input tokens and $10 per 1 million output tokens. Conversely, o1 costs $15 per 1 million input tokens and $60 per 1 million output tokens. Moreover, o1 is approximately 40 times slower than GPT-4o, with 29.7 seconds to first token as opposed to GPT-4o's 0.72. The latency of o3, while not publicly available, is presumably even higher. o1 and o3's strong capabilities are likely to continue fueling powerful AI systems and agents.\nOpenAI first released o1-preview to ChatGPT Plus and Teams users on Sept. 12, 2024, and released the full version of o1 (as well as access to ChatGPT Pro, a $200 monthly subscription enabling access to o1) on Dec. 5, 2024.\n\n### EVALUATION TASK ###\n\"instructions\": \"YOUR SPECIFIC ROLE: Agent 3 - The Diagnostic Evaluator\\n\\n### EVALUATION TASK ###\\n\\nYour task is to provide a comprehensive diagnostic report on the performance of Agent 1 and Agent 2. Compare their outputs against the Ground Truth Context. Your report must be detailed, objective, and follow the presentation format below precisely.\\n\\n### CRITICAL REPORTING FORMAT ###\\n\\nYour entire output must adhere to this Markdown structure:\\n\\n# RAG Performance Report\\n\\n**Overall Verdict:** [Provide a one-word verdict: FLAWLESS, SUCCESSFUL, PARTIAL_FAILURE, or COMPLETE_FAILURE] \\n**Final Score:** [Provide a score out of 10]\\n\\n---\\n\\n## Agent 1: Strategic Retrieval Analysis\\n\\n* **Query Strategy Effectiveness:** [Score 1-5] - Assess the quality and diversity of the search queries Agent 1 generated. Were they well-chosen for the user's prompt?\\n* **Initial Retrieval Quality:** [Score 1-5] - How relevant and complete was the context Agent 1 retrieved? Did it contain the necessary information? Was there excessive irrelevant noise?\\n* **Diagnosis:** [Briefly explain your reasoning for the scores above.]\\n\\n---\\n\\n## Agent 2: Refinement & Synthesis Analysis\\n\\n* **Gap Identification Skill:** [Score 1-5, or N/A if no refinement was needed] - How well did Agent 2 identify missing information from Agent 1's context? \\n* **Final Answer Accuracy:** [Score 1-5] - Is the final answer factually correct when compared to the ground truth? Note any specific errors.\\n* **Final Answer Completeness:** [Score 1-5] - Does the final answer address all parts of the user's original query?\\n* **Diagnosis:** [Explain your reasoning, specifically noting whether Agent 2 successfully corrected for Agent 1's flaws or if it introduced new errors.]\\n\\n---\\n\\n## Discrepancy Analysis\\n\\n| Key Information Point | Ground Truth | Agent 2's Final Answer |\\n| :--- | :--- | :--- |\\n| [e.g., o1 MATH Score] | [e.g., 94.80%] | [e.g., '94.80%' or 'Not Mentioned' or 'Incorrect value'] |\\n| [e.g., GPT-4o Cost] | [e.g., $2.50 input / $10 output] | [e.g., '$2.50 input / $10 output'] |\\n| ... | ... | ... |\\n\\n\\n### Final Recommendations\\n[Provide a 1-2 sentence recommendation on the weakest part of the chain. For example: \\\"Agent 1's HyDE query was poor, leading to irrelevant context.\\\" or \\\"Agent 2 failed to identify the missing cost data and should have performed a follow-up retrieval.\\\"]\"", "thinking_enabled": false}]}