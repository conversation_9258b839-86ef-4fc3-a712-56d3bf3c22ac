# MAIAChat Desktop - Environment Variables Template
# Created by <PERSON><PERSON><PERSON><PERSON> - MAIAChat.com
#
# Copy this file to .env and fill in your API keys
# Alternative to using config.json for API key storage

# ============================================================================
# AI PROVIDERS - Add your API keys here
# ============================================================================

# OpenAI (GPT-4, GPT-3.5-turbo, etc.)
# Get your key at: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic (<PERSON> models)
# Get your key at: https://console.anthropic.com/
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Google Gemini
# Get your key at: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# Groq (Fast inference)
# Get your key at: https://console.groq.com/keys
GROQ_API_KEY=your_groq_api_key_here

# xAI Grok
# Get your key at: https://console.x.ai/
GROK_API_KEY=your_grok_api_key_here

# DeepSeek
# Get your key at: https://platform.deepseek.com/api_keys
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# OpenRouter (Access to multiple models)
# Get your key at: https://openrouter.ai/keys
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Requesty
# Get your key at: https://requesty.ai/
REQUESTY_API_KEY=your_requesty_api_key_here

# ============================================================================
# SEARCH PROVIDERS (Optional)
# ============================================================================

# Google Search API
# Get your key at: https://console.developers.google.com/
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id_here

# Serper Search
# Get your key at: https://serper.dev/
SERPER_API_KEY=your_serper_api_key_here

# Brave Search
# Get your key at: https://api.search.brave.com/
BRAVE_SEARCH_API_KEY=your_brave_search_api_key_here

# ============================================================================
# APPLICATION SETTINGS (Optional)
# ============================================================================

# RAG (Knowledge Base) Settings
RAG_DISABLED=false
RAG_N_RESULTS=30
RAG_TOKEN_LIMIT=8000
RAG_RERANKING=true
EMBEDDING_DEVICE=auto

# Response Context Limit
MAX_RESPONSE_CONTEXT=128000

# Profile Settings
LAST_PROFILE_NAME=Default Profile
LAST_PROFILE_IS_EXAMPLE=true

# ============================================================================
# NOTES
# ============================================================================
#
# 1. Remove the lines for services you don't plan to use
# 2. Never commit this file with real API keys
# 3. The application will use these values if config.json doesn't exist
# 4. Environment variables take precedence over config.json values
# 5. You only need API keys for the services you want to use
