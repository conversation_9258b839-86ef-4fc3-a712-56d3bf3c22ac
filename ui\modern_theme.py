# modern_theme.py - Modern UI Design System for Python Agents

from PyQt6.QtCore import QPropertyAnimation, QEasingCurve, QRect, QTimer
from PyQt6.QtWidgets import QGraphicsDropShadowEffect
from PyQt6.QtGui import QColor
import os


class ModernTheme:
    """Modern design system with dark/light themes, animations, and professional styling."""
    
    def __init__(self):
        self.current_theme = "dark"  # Default to dark theme
        self.setup_themes()
    
    def setup_themes(self):
        """Set up color schemes for dark and light themes."""
        self.themes = {
            "dark": {
                # Primary colors
                "primary": "#2563eb",           # Blue-600
                "primary_hover": "#1d4ed8",     # Blue-700
                "primary_light": "#3b82f6",     # Blue-500
                "accent": "#10b981",            # Emerald-500
                "accent_hover": "#059669",      # Emerald-600
                
                # Background colors
                "bg_primary": "#0f172a",        # Slate-900
                "bg_secondary": "#1e293b",      # Slate-800
                "bg_tertiary": "#334155",       # Slate-700
                "bg_card": "#1e293b",           # Slate-800
                "bg_input": "#334155",          # Slate-700
                "bg_hover": "#475569",          # Slate-600
                
                # Text colors
                "text_primary": "#f8fafc",      # Slate-50
                "text_secondary": "#cbd5e1",    # Slate-300
                "text_muted": "#94a3b8",        # Slate-400
                "text_placeholder": "#64748b",  # Slate-500
                
                # Border colors
                "border": "#475569",            # Slate-600
                "border_light": "#64748b",      # Slate-500
                "border_focus": "#2563eb",      # Blue-600
                
                # Status colors
                "success": "#10b981",           # Emerald-500
                "warning": "#f59e0b",           # Amber-500
                "error": "#ef4444",             # Red-500
                "info": "#3b82f6",              # Blue-500
                
                # Agent colors (modern palette)
                "agent_1": "#8b5cf6",           # Violet-500
                "agent_2": "#06b6d4",           # Cyan-500
                "agent_3": "#10b981",           # Emerald-500
                "agent_4": "#f59e0b",           # Amber-500
                "agent_5": "#ef4444",           # Red-500
            },
            "light": {
                # Primary colors
                "primary": "#2563eb",           # Blue-600
                "primary_hover": "#1d4ed8",     # Blue-700
                "primary_light": "#3b82f6",     # Blue-500
                "accent": "#10b981",            # Emerald-500
                "accent_hover": "#059669",      # Emerald-600
                
                # Background colors
                "bg_primary": "#ffffff",        # White
                "bg_secondary": "#f8fafc",      # Slate-50
                "bg_tertiary": "#f1f5f9",       # Slate-100
                "bg_card": "#ffffff",           # White
                "bg_input": "#f8fafc",          # Slate-50
                "bg_hover": "#f1f5f9",          # Slate-100
                
                # Text colors
                "text_primary": "#0f172a",      # Slate-900
                "text_secondary": "#334155",    # Slate-700
                "text_muted": "#64748b",        # Slate-500
                "text_placeholder": "#94a3b8",  # Slate-400
                
                # Border colors
                "border": "#e2e8f0",            # Slate-200
                "border_light": "#cbd5e1",      # Slate-300
                "border_focus": "#2563eb",      # Blue-600
                
                # Status colors
                "success": "#10b981",           # Emerald-500
                "warning": "#f59e0b",           # Amber-500
                "error": "#ef4444",             # Red-500
                "info": "#3b82f6",              # Blue-500
                
                # Agent colors (modern palette)
                "agent_1": "#8b5cf6",           # Violet-500
                "agent_2": "#06b6d4",           # Cyan-500
                "agent_3": "#10b981",           # Emerald-500
                "agent_4": "#f59e0b",           # Amber-500
                "agent_5": "#ef4444",           # Red-500
            }
        }
    
    def get_color(self, color_name):
        """Get color from current theme."""
        return self.themes[self.current_theme].get(color_name, "#000000")
    
    def toggle_theme(self):
        """Toggle between dark and light themes."""
        self.current_theme = "light" if self.current_theme == "dark" else "dark"
    
    def get_main_window_style(self):
        """Get main window stylesheet."""
        return f"""
        QMainWindow {{
            background-color: {self.get_color('bg_primary')};
            color: {self.get_color('text_primary')};
            font-family: 'Segoe UI', 'San Francisco', 'Helvetica Neue', sans-serif;
            font-size: 14px;
        }}
        
        QMainWindow::separator {{
            background-color: {self.get_color('border')};
            width: 1px;
            height: 1px;
        }}
        
        /* Scrollbars */
        QScrollBar:vertical {{
            background-color: {self.get_color('bg_secondary')};
            width: 12px;
            border: none;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:vertical {{
            background-color: {self.get_color('bg_hover')};
            border-radius: 6px;
            min-height: 20px;
            margin: 2px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background-color: {self.get_color('border_light')};
        }}
        
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            border: none;
            background: none;
        }}
        
        QScrollBar:horizontal {{
            background-color: {self.get_color('bg_secondary')};
            height: 12px;
            border: none;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:horizontal {{
            background-color: {self.get_color('bg_hover')};
            border-radius: 6px;
            min-width: 20px;
            margin: 2px;
        }}
        
        QScrollBar::handle:horizontal:hover {{
            background-color: {self.get_color('border_light')};
        }}
        
        QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
            border: none;
            background: none;
        }}
        """
    
    def get_button_style(self, variant="primary"):
        """Get button stylesheet for different variants."""
        if variant == "primary":
            bg_color = self.get_color('primary')
            hover_color = self.get_color('primary_hover')
            text_color = "#ffffff"
        elif variant == "secondary":
            bg_color = self.get_color('bg_tertiary')
            hover_color = self.get_color('bg_hover')
            text_color = self.get_color('text_primary')
        elif variant == "success":
            bg_color = self.get_color('success')
            hover_color = self.get_color('accent_hover')
            text_color = "#ffffff"
        elif variant == "warning":
            bg_color = self.get_color('warning')
            hover_color = "#d97706"  # Amber-600
            text_color = "#ffffff"
        elif variant == "danger":
            bg_color = self.get_color('error')
            hover_color = "#dc2626"  # Red-600
            text_color = "#ffffff"
        else:
            bg_color = self.get_color('bg_tertiary')
            hover_color = self.get_color('bg_hover')
            text_color = self.get_color('text_primary')
        
        return f"""
        QPushButton {{
            background-color: {bg_color};
            color: {text_color};
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: 600;
            font-size: 14px;
            min-height: 20px;
        }}
        
        QPushButton:hover {{
            background-color: {hover_color};
        }}
        
        QPushButton:pressed {{
            background-color: {hover_color};
            transform: translateY(1px);
        }}
        
        QPushButton:disabled {{
            background-color: {self.get_color('bg_hover')};
            color: {self.get_color('text_muted')};
        }}
        """
    
    def get_input_style(self):
        """Get input field stylesheet."""
        return f"""
        QTextEdit, QLineEdit, QSpinBox {{
            background-color: {self.get_color('bg_input')};
            color: {self.get_color('text_primary')};
            border: 2px solid {self.get_color('border')};
            border-radius: 8px;
            padding: 12px;
            font-size: 14px;
            selection-background-color: {self.get_color('primary_light')};
        }}
        
        QTextEdit:focus, QLineEdit:focus, QSpinBox:focus {{
            border-color: {self.get_color('border_focus')};
            outline: none;
        }}
        
        QSpinBox::up-button, QSpinBox::down-button {{
            background-color: {self.get_color('bg_hover')};
            border: none;
            border-radius: 4px;
            width: 20px;
        }}
        
        QSpinBox::up-button:hover, QSpinBox::down-button:hover {{
            background-color: {self.get_color('border_light')};
        }}
        """
    
    def get_combobox_style(self):
        """Get combobox stylesheet."""
        return f"""
        QComboBox {{
            background-color: {self.get_color('bg_input')};
            color: {self.get_color('text_primary')};
            border: 2px solid {self.get_color('border')};
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 14px;
            min-height: 20px;
        }}
        
        QComboBox:focus {{
            border-color: {self.get_color('border_focus')};
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 30px;
            border-top-right-radius: 8px;
            border-bottom-right-radius: 8px;
        }}
        
        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid {self.get_color('text_secondary')};
            margin-right: 10px;
        }}
        
        QComboBox QAbstractItemView {{
            background-color: {self.get_color('bg_card')};
            color: {self.get_color('text_primary')};
            border: 1px solid {self.get_color('border')};
            border-radius: 8px;
            padding: 8px;
            selection-background-color: {self.get_color('primary_light')};
        }}
        
        QComboBox QAbstractItemView::item {{
            padding: 8px 12px;
            border-radius: 4px;
        }}
        
        QComboBox QAbstractItemView::item:hover {{
            background-color: {self.get_color('bg_hover')};
        }}
        """
    
    def get_card_style(self, agent_number=None):
        """Get card/panel stylesheet."""
        border_color = self.get_color('border')
        if agent_number:
            border_color = self.get_color(f'agent_{agent_number}')
        
        return f"""
        QFrame {{
            background-color: {self.get_color('bg_card')};
            border: 2px solid {border_color};
            border-radius: 12px;
            padding: 16px;
            margin: 8px;
        }}
        
        QFrame:hover {{
            border-color: {self.get_color('border_focus')};
        }}
        """
    
    def get_tab_style(self):
        """Get tab widget stylesheet."""
        return f"""
        QTabWidget::pane {{
            border: 2px solid {self.get_color('border')};
            border-radius: 8px;
            background-color: {self.get_color('bg_card')};
            margin-top: 2px;
        }}
        
        QTabWidget::tab-bar {{
            alignment: center;
        }}
        
        QTabBar::tab {{
            background-color: {self.get_color('bg_secondary')};
            color: {self.get_color('text_secondary')};
            border: 2px solid {self.get_color('border')};
            border-bottom: none;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            padding: 12px 24px;
            margin-right: 4px;
            font-weight: 600;
        }}
        
        QTabBar::tab:selected {{
            background-color: {self.get_color('primary')};
            color: #ffffff;
            border-color: {self.get_color('primary')};
        }}
        
        QTabBar::tab:hover:!selected {{
            background-color: {self.get_color('bg_hover')};
            color: {self.get_color('text_primary')};
        }}
        """
    
    def get_checkbox_style(self):
        """Get checkbox stylesheet."""
        return f"""
        QCheckBox {{
            color: {self.get_color('text_primary')};
            font-size: 14px;
            spacing: 8px;
        }}
        
        QCheckBox::indicator {{
            width: 20px;
            height: 20px;
            border: 2px solid {self.get_color('border')};
            border-radius: 4px;
            background-color: {self.get_color('bg_input')};
        }}
        
        QCheckBox::indicator:hover {{
            border-color: {self.get_color('border_focus')};
        }}
        
        QCheckBox::indicator:checked {{
            background-color: {self.get_color('primary')};
            border-color: {self.get_color('primary')};
            image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQgTDQuNSA3LjUgTDExIDEiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
        }}
        """
    
    def get_label_style(self, variant="default"):
        """Get label stylesheet for different variants."""
        if variant == "heading":
            return f"""
            QLabel {{
                color: {self.get_color('text_primary')};
                font-size: 20px;
                font-weight: 700;
                margin: 12px 0px;
            }}
            """
        elif variant == "subheading":
            return f"""
            QLabel {{
                color: {self.get_color('text_secondary')};
                font-size: 16px;
                font-weight: 600;
                margin: 8px 0px;
            }}
            """
        elif variant == "caption":
            return f"""
            QLabel {{
                color: {self.get_color('text_muted')};
                font-size: 12px;
                font-weight: 400;
            }}
            """
        else:
            return f"""
            QLabel {{
                color: {self.get_color('text_primary')};
                font-size: 14px;
                font-weight: 400;
            }}
            """
    
    def get_list_style(self):
        """Get list widget stylesheet."""
        return f"""
        QListWidget {{
            background-color: {self.get_color('bg_card')};
            color: {self.get_color('text_primary')};
            border: 2px solid {self.get_color('border')};
            border-radius: 8px;
            padding: 8px;
            font-size: 14px;
        }}
        
        QListWidget::item {{
            padding: 12px;
            border-radius: 6px;
            margin: 2px;
        }}
        
        QListWidget::item:hover {{
            background-color: {self.get_color('bg_hover')};
        }}
        
        QListWidget::item:selected {{
            background-color: {self.get_color('primary')};
            color: #ffffff;
        }}
        """
    
    def add_shadow_effect(self, widget, blur_radius=15, offset=(0, 4)):
        """Add modern drop shadow to widget."""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(blur_radius)
        shadow.setColor(QColor(0, 0, 0, 50))  # Semi-transparent black
        shadow.setOffset(offset[0], offset[1])
        widget.setGraphicsEffect(shadow)
    
    def create_fade_animation(self, widget, duration=300):
        """Create fade animation for widget."""
        animation = QPropertyAnimation(widget, b"windowOpacity")
        animation.setDuration(duration)
        animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        return animation
    
    def create_slide_animation(self, widget, start_rect, end_rect, duration=300):
        """Create slide animation for widget."""
        animation = QPropertyAnimation(widget, b"geometry")
        animation.setDuration(duration)
        animation.setStartValue(QRect(*start_rect))
        animation.setEndValue(QRect(*end_rect))
        animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        return animation


# Global theme instance
modern_theme = ModernTheme()