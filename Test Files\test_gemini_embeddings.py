#!/usr/bin/env python3
"""
Test script for Gemini embedding functionality in RAG system.
This script tests the new Gemini embedding integration with the updated API.
"""

import os
import sys
import json
import logging
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from rag_handler import <PERSON><PERSON><PERSON>and<PERSON>, EmbeddingProvider
import numpy as np

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_gemini_embeddings():
    """Test Gemini embedding functionality."""
    print("=" * 60)
    print("TESTING GEMINI EMBEDDING INTEGRATION")
    print("=" * 60)
    
    # Check if Gemini API key is available
    gemini_api_key = os.getenv("GEMINI_API_KEY")
    has_api_key = bool(gemini_api_key)
    
    if not has_api_key:
        print("⚠️  GEMINI_API_KEY not found in environment variables.")
        print("   Please set your Gemini API key to run the full test.")
        print("   Running basic integration test without actual API calls...")
    
    # Create a temporary config file for testing
    test_config_path = "test_config_gemini.json"
    test_config = {
        "GEMINI_API_KEY": gemini_api_key or "placeholder_key",
        "EMBEDDING_PROVIDER": "gemini",
        "EMBEDDING_MODEL": "embedding-001"
    }
    
    with open(test_config_path, "w") as f:
        json.dump(test_config, f, indent=4)
    
    # Test data
    test_texts = [
        "This is a test document about artificial intelligence and machine learning.",
        "Gemini is Google's latest AI model that supports embedding generation.",
        "RAG (Retrieval-Augmented Generation) combines search with language models.",
        "Embedding models convert text into numerical vector representations.",
    ]
    
    print(f"\n📝 Test texts prepared: {len(test_texts)} samples")
    
    # Test different Gemini models
    gemini_models = ["gemini-embedding-001", "text-embedding-004"]
    
    for model_name in gemini_models:
        print(f"\n🧪 Testing Gemini model: {model_name}")
        print("-" * 40)
        
        try:
            # Initialize RAG handler with Gemini provider
            rag_handler = RAGHandler(
                persist_directory="test_gemini_db",
                config_path=test_config_path,
                embedding_provider=EmbeddingProvider.GEMINI,
                embedding_model=model_name
            )
            
            print(f"✅ RAG Handler initialized successfully")
            print(f"   Provider: {rag_handler.embedding_provider.value}")
            print(f"   Model: {rag_handler.embedding_model_name}")
            print(f"   Dimension: {rag_handler.dimension}")
            
            if has_api_key:
                # Test embedding generation
                print(f"\n🔄 Generating embeddings...")
                try:
                    embeddings = rag_handler._get_embeddings(test_texts)
                    
                    if embeddings.size > 0:
                        print(f"✅ Embeddings generated successfully!")
                        print(f"   Shape: {embeddings.shape}")
                        print(f"   Data type: {embeddings.dtype}")
                        print(f"   Sample values: {embeddings[0][:5]}")
                        
                        # Validate embedding properties
                        if embeddings.shape[0] == len(test_texts):
                            print(f"✅ Correct number of embeddings generated")
                        else:
                            print(f"❌ Expected {len(test_texts)} embeddings, got {embeddings.shape[0]}")
                        
                        if embeddings.shape[1] == rag_handler.dimension:
                            print(f"✅ Correct embedding dimension: {rag_handler.dimension}")
                        else:
                            print(f"❌ Expected dimension {rag_handler.dimension}, got {embeddings.shape[1]}")
                        
                        # Test similarity
                        print(f"\n🔍 Testing embedding similarity...")
                        # Normalize embeddings for cosine similarity
                        normalized_embeddings = embeddings / np.linalg.norm(embeddings, axis=1, keepdims=True)
                        similarity_matrix = np.dot(normalized_embeddings, normalized_embeddings.T)
                        
                        print(f"✅ Similarity matrix computed:")
                        print(f"   Shape: {similarity_matrix.shape}")
                        print(f"   Diagonal (self-similarity): {np.diag(similarity_matrix)}")
                        
                    else:
                        print(f"❌ No embeddings generated")
                        
                except Exception as e:
                    print(f"❌ Error generating embeddings: {e}")
                    print(f"   This might be due to API key issues or network connectivity")
            else:
                print(f"⏭️  Skipping embedding generation test (no API key)")
            
        except Exception as e:
            print(f"❌ Error initializing RAG handler: {e}")
            logger.error(f"Full error: {e}", exc_info=True)
        
        # Cleanup
        try:
            import shutil
            if Path("test_gemini_db").exists():
                shutil.rmtree("test_gemini_db")
        except:
            pass
    
    # Cleanup test config
    try:
        os.remove(test_config_path)
    except:
        pass
    
    print(f"\n" + "=" * 60)
    print("GEMINI EMBEDDING TEST COMPLETED")
    print("=" * 60)

def test_config_integration():
    """Test configuration integration with RAG settings."""
    print(f"\n🔧 Testing configuration integration...")
    
    # Test configuration loading
    test_config_data = {
        "EMBEDDING_PROVIDER": "gemini",
        "EMBEDDING_MODEL": "embedding-001",
        "EMBEDDING_DEVICE": "cpu",
        "GEMINI_API_KEY": "test_key"
    }
    
    # Write test config
    with open("test_config.json", "w") as f:
        json.dump(test_config_data, f, indent=4)
    
    try:
        # Test if config loads correctly
        from config_manager import ConfigManager
        config_manager = ConfigManager()
        
        print(f"✅ Configuration integration test passed")
        
    except Exception as e:
        print(f"❌ Configuration integration test failed: {e}")
    
    # Cleanup
    try:
        os.remove("test_config.json")
    except:
        pass

def test_ui_integration():
    """Test UI integration (without actually opening UI)."""
    print(f"\n🖥️  Testing UI integration...")
    
    try:
        from rag_settings_dialog import RAGSettingsDialog
        from PyQt6.QtWidgets import QApplication
        import sys
        
        # This would normally require X11/display, so we'll just test imports
        print(f"✅ RAG Settings Dialog import successful")
        print(f"✅ UI integration components available")
        
        # Test model options
        dialog_class = RAGSettingsDialog
        # We can't instantiate without QApplication, but we can check methods exist
        if hasattr(dialog_class, 'update_embedding_models'):
            print(f"✅ Embedding model update method available")
        else:
            print(f"❌ Embedding model update method missing")
            
    except Exception as e:
        print(f"❌ UI integration test failed: {e}")

if __name__ == "__main__":
    try:
        print("🚀 Starting Gemini Embedding Integration Tests...")
        
        # Core embedding functionality test
        test_gemini_embeddings()
        
        # Configuration integration test
        test_config_integration()
        
        # UI integration test
        test_ui_integration()
        
        print(f"\n🎉 All tests completed!")
        print(f"\nTo use Gemini embeddings in your RAG system:")
        print(f"1. Set your GEMINI_API_KEY environment variable")
        print(f"2. Open RAG Settings and select 'gemini' as provider")
        print(f"3. Choose your preferred Gemini embedding model")
        print(f"4. Click OK to save settings")
        
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        logger.error(f"Test execution error: {e}", exc_info=True)
        sys.exit(1)